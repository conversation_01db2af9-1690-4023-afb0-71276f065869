# Test Files

This folder contains all test files for the web media player and document editor project.

## Test Categories

### HTML Test Files
- `test-*.html` - Various feature-specific test pages
- `final-test.html` - Final verification test page
- `debug-navigation*.html` - Navigation debugging tests
- `minimal-navigation-test.html` - Minimal navigation test

### Test Documents
- `test-document.txt` - Sample text document for testing
- `test-sample.docx` - Sample Word document with quotes and footnotes
- `test_text_file.docx` - Test file for file type detection

### Test Scripts
- `test-ollama-vision.py` - Python script to test Ollama vision AI functionality
- `test-instructions.md` - Detailed testing instructions for Word document editing

### Feature-Specific Tests
- `test-auto-start.html` - Auto-start functionality test
- `test-current-state.html` - Current state verification
- `test-fixed-app.html` - Fixed application test
- `test-functionality.html` - General functionality test
- `test-mistral-integration.html` - Mistral AI integration test
- `test-navigation.html` - Navigation system test
- `test-pdf-converter.html` - PDF converter functionality test
- `test-pronunciation-analysis.html` - Pronunciation analysis test
- `test-quote-footnote.html` - Quote and footnote inserter test
- `test-tab-navigation.html` - Tab navigation test
- `test-whisper.html` - Whisper transcription test

### Verification and Debug Files
- `final-verification.html` - Final verification page
- `success-confirmation.html` - Success confirmation page
- `import-error-resolved.html` - Import error resolution test

## Usage

Most test files can be opened directly in a browser while the main application is running on `http://localhost:8000` or the appropriate development server port.

For Python test scripts, run them from the command line:
```bash
python test-ollama-vision.py
```

## Notes

- These test files were moved from the root directory to improve project organization
- Test files may reference the main application running on localhost
- Some tests require specific services (Whisper, Mistral, Ollama) to be running
