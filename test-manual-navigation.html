<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Navigation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            display: block;
            width: 200px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .console-output {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Manual Navigation Test</h1>
    
    <div class="status">
        This page will test navigation by directly calling the functions.
    </div>
    
    <button class="test-button" onclick="testVerificationView()">Test Verification View</button>
    <button class="test-button" onclick="testTextEditorView()">Test Text Editor View</button>
    <button class="test-button" onclick="testAiVoiceCreatorView()">Test AI Voice Creator View</button>
    <button class="test-button" onclick="testQuoteFootnoteView()">Test Quote Footnote View</button>
    <button class="test-button" onclick="testPdfConverterView()">Test PDF Converter View</button>
    <button class="test-button" onclick="testAbbreviatedTextView()">Test Abbreviated Text View</button>
    <button class="test-button" onclick="openMainAppAndTest()">Open Main App & Test</button>
    
    <div class="console-output" id="console"></div>
    
    <script>
        function log(message) {
            const console = document.getElementById('console');
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            console.appendChild(div);
            console.scrollTop = console.scrollHeight;
        }
        
        function testVerificationView() {
            log('Testing verification view...');
            try {
                // Try to access the main app window
                const mainWindow = window.open('http://localhost:5174', 'mainApp');
                setTimeout(() => {
                    try {
                        const btn = mainWindow.document.getElementById('audiobook-verification-btn');
                        if (btn) {
                            log('Found verification button, clicking...');
                            btn.click();
                            log('Verification button clicked successfully');
                        } else {
                            log('ERROR: Verification button not found');
                        }
                    } catch (e) {
                        log('ERROR: ' + e.message);
                    }
                }, 1000);
            } catch (e) {
                log('ERROR: ' + e.message);
            }
        }
        
        function testTextEditorView() {
            log('Testing text editor view...');
            try {
                const mainWindow = window.open('http://localhost:5174', 'mainApp');
                setTimeout(() => {
                    try {
                        const btn = mainWindow.document.getElementById('audiobook-text-editor-btn');
                        if (btn) {
                            log('Found text editor button, clicking...');
                            btn.click();
                            log('Text editor button clicked successfully');
                        } else {
                            log('ERROR: Text editor button not found');
                        }
                    } catch (e) {
                        log('ERROR: ' + e.message);
                    }
                }, 1000);
            } catch (e) {
                log('ERROR: ' + e.message);
            }
        }
        
        function testAiVoiceCreatorView() {
            log('Testing AI voice creator view...');
            try {
                const mainWindow = window.open('http://localhost:5174', 'mainApp');
                setTimeout(() => {
                    try {
                        const btn = mainWindow.document.getElementById('ai-voice-creator-btn');
                        if (btn) {
                            log('Found AI voice creator button, clicking...');
                            btn.click();
                            log('AI voice creator button clicked successfully');
                        } else {
                            log('ERROR: AI voice creator button not found');
                        }
                    } catch (e) {
                        log('ERROR: ' + e.message);
                    }
                }, 1000);
            } catch (e) {
                log('ERROR: ' + e.message);
            }
        }
        
        function testQuoteFootnoteView() {
            log('Testing quote footnote view...');
            try {
                const mainWindow = window.open('http://localhost:5174', 'mainApp');
                setTimeout(() => {
                    try {
                        const btn = mainWindow.document.getElementById('quote-footnote-inserter-btn');
                        if (btn) {
                            log('Found quote footnote button, clicking...');
                            btn.click();
                            log('Quote footnote button clicked successfully');
                        } else {
                            log('ERROR: Quote footnote button not found');
                        }
                    } catch (e) {
                        log('ERROR: ' + e.message);
                    }
                }, 1000);
            } catch (e) {
                log('ERROR: ' + e.message);
            }
        }
        
        function testPdfConverterView() {
            log('Testing PDF converter view...');
            try {
                const mainWindow = window.open('http://localhost:5174', 'mainApp');
                setTimeout(() => {
                    try {
                        const btn = mainWindow.document.getElementById('pdf-to-text-converter-btn');
                        if (btn) {
                            log('Found PDF converter button, clicking...');
                            btn.click();
                            log('PDF converter button clicked successfully');
                        } else {
                            log('ERROR: PDF converter button not found');
                        }
                    } catch (e) {
                        log('ERROR: ' + e.message);
                    }
                }, 1000);
            } catch (e) {
                log('ERROR: ' + e.message);
            }
        }
        
        function testAbbreviatedTextView() {
            log('Testing abbreviated text view...');
            try {
                const mainWindow = window.open('http://localhost:5174', 'mainApp');
                setTimeout(() => {
                    try {
                        const btn = mainWindow.document.getElementById('abbreviated-text-expander-btn');
                        if (btn) {
                            log('Found abbreviated text button, clicking...');
                            btn.click();
                            log('Abbreviated text button clicked successfully');
                        } else {
                            log('ERROR: Abbreviated text button not found');
                        }
                    } catch (e) {
                        log('ERROR: ' + e.message);
                    }
                }, 1000);
            } catch (e) {
                log('ERROR: ' + e.message);
            }
        }
        
        function openMainAppAndTest() {
            log('Opening main app for manual testing...');
            window.open('http://localhost:5174', '_blank');
            log('Main app opened - test the navigation buttons manually');
        }
        
        window.addEventListener('load', () => {
            log('Manual navigation test page loaded');
            log('Click the buttons above to test navigation functionality');
        });
    </script>
</body>
</html>
