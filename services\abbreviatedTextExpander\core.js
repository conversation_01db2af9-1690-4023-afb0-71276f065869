/********************************************************************
 *  core.js - Abbreviated Text Expander Core Module
 *  ---------------------------------------------------------------
 *  Main initialization and coordination for the text expander
 *******************************************************************/

import * as dom from '../../domElements.js';
import * as state from '../../state.js';
import { downloadBlob } from '../../utils.js';
import { GEMINI_MODELS, DEFAULT_GEMINI_MODEL, GEMINI_API_KEY } from '../../constants.js';
import { initializeAI, testGeminiConnection } from './ai.js';
import { initializeTextProcessing } from './textProcessing.js';
import { initializeUIHandlers } from './uiHandlers.js';
import { initializeDropdowns } from './utils.js';
import { convertHtmlToDocxBlob } from '../docxGenerator.js';

// Current document state
export let currentDocument = null;
export let selectedText = '';
export let selectedRange = null;
export let geminiApiKey = '';
export let documentHistory = [];
export let historyIndex = -1;
export let isSelecting = false;
export let selectionStartPos = null;

// Initialize with environment variable if available
if (GEMINI_API_KEY && !geminiApiKey) {
    geminiApiKey = GEMINI_API_KEY;
}

/**
 * Initialize the Abbreviated Text Expander
 */
export function initializeAbbreviatedTextExpander() {
    console.log('Initializing Abbreviated Text Expander...');

    // Debug: Check if button elements exist
    console.log('Button elements check:');
    console.log('- selectAllTextBtn:', !!dom.selectAllTextBtn);
    console.log('- clearSelectionBtn:', !!dom.clearSelectionBtn);
    console.log('- undoChangesBtn:', !!dom.undoChangesBtn);
    console.log('- redoChangesBtn:', !!dom.redoChangesBtn);
    console.log('- runAllFormattingBtn:', !!dom.runAllFormattingBtn);
    console.log('- abbreviatedTextExpanderSaveBtn:', !!dom.abbreviatedTextExpanderSaveBtn);

    // Initialize all modules
    initializeAI();
    initializeTextProcessing();
    initializeUIHandlers();
    initializeDropdowns();

    // Set up document loading
    setupDocumentHandling();

    // Initialize mobile optimizations
    initializeMobileOptimizations();

    // Initialize prompt template and model
    updateInstructionsPreview();

    // Initial button state update
    console.log('Performing initial button state update...');
    updateButtonStates();

    console.log('Abbreviated Text Expander initialized successfully');
}

/**
 * Initialize mobile-specific optimizations
 */
function initializeMobileOptimizations() {
    console.log('Initializing mobile optimizations...');

    // More conservative mobile detection - only apply on actual mobile devices
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isSmallScreen = window.innerWidth <= 768;
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // Only apply mobile optimizations if it's actually a mobile device OR a small touch screen
    if ((isMobile && isTouch) || (isSmallScreen && isTouch)) {
        console.log('Mobile device detected, applying mobile optimizations...');

        // Add mobile class to body for CSS targeting
        document.body.classList.add('mobile-device');

        // Set up touch gestures for document editor
        setupTouchGestures();

        // Optimize viewport for mobile
        optimizeViewport();

        // Set up mobile-specific event handlers
        setupMobileEventHandlers();

        // Improve text selection on mobile
        improveMobileTextSelection();
    } else {
        console.log('Desktop device detected, skipping mobile optimizations');
    }
}

/**
 * Set up touch gestures for better mobile interaction
 */
function setupTouchGestures() {
    const textArea = dom.abbreviatedTextExpanderTextArea;
    if (!textArea) return;

    let touchStartTime = 0;
    let touchStartPos = { x: 0, y: 0 };

    // Handle touch start
    textArea.addEventListener('touchstart', (e) => {
        touchStartTime = Date.now();
        if (e.touches.length === 1) {
            touchStartPos.x = e.touches[0].clientX;
            touchStartPos.y = e.touches[0].clientY;
        }
    }, { passive: true });

    // Handle touch end for tap gestures
    textArea.addEventListener('touchend', (e) => {
        const touchEndTime = Date.now();
        const touchDuration = touchEndTime - touchStartTime;

        // Quick tap (less than 200ms) - select word or paragraph
        if (touchDuration < 200 && e.changedTouches.length === 1) {
            const touch = e.changedTouches[0];
            const deltaX = Math.abs(touch.clientX - touchStartPos.x);
            const deltaY = Math.abs(touch.clientY - touchStartPos.y);

            // If it's a tap (minimal movement)
            if (deltaX < 10 && deltaY < 10) {
                handleMobileTap(touch.clientX, touch.clientY);
            }
        }
    }, { passive: true });
}

/**
 * Handle mobile tap gestures for smart text selection
 */
function handleMobileTap(x, y) {
    const textArea = dom.abbreviatedTextExpanderTextArea;
    if (!textArea) return;

    // Get the element at the touch point
    const element = document.elementFromPoint(x, y);
    if (!element || !textArea.contains(element)) return;

    // Create a range at the touch point
    const range = document.caretRangeFromPoint(x, y);
    if (!range) return;

    // Expand selection to word boundaries
    const selection = window.getSelection();
    selection.removeAllRanges();

    // Expand to word
    range.expand('word');
    selection.addRange(range);

    // Update selected text
    updateSelectedText();
}

/**
 * Optimize viewport settings for mobile
 */
function optimizeViewport() {
    // Ensure viewport meta tag is properly set
    let viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport) {
        viewport = document.createElement('meta');
        viewport.name = 'viewport';
        document.head.appendChild(viewport);
    }

    // Set mobile-optimized viewport
    viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover';
}

/**
 * Set up mobile-specific event handlers
 */
function setupMobileEventHandlers() {
    // Set up mobile config toggle
    setupMobileConfigToggle();

    // Prevent zoom on double tap for buttons
    const buttons = document.querySelectorAll('.control-button');
    buttons.forEach(button => {
        button.addEventListener('touchend', (e) => {
            e.preventDefault();
            button.click();
        });
    });

    // Handle orientation change
    window.addEventListener('orientationchange', () => {
        setTimeout(() => {
            // Recalculate layout after orientation change
            updateButtonStates();

            // Scroll to top of document if needed
            const textArea = dom.abbreviatedTextExpanderTextArea;
            if (textArea && textArea.scrollTop > 0) {
                textArea.scrollTop = 0;
            }
        }, 100);
    });

    // Handle keyboard show/hide on mobile
    if (window.visualViewport) {
        window.visualViewport.addEventListener('resize', () => {
            const textArea = dom.abbreviatedTextExpanderTextArea;
            if (textArea && document.activeElement === textArea) {
                // Adjust layout when virtual keyboard appears
                textArea.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    }
}

/**
 * Improve text selection behavior on mobile
 */
function improveMobileTextSelection() {
    const textArea = dom.abbreviatedTextExpanderTextArea;
    if (!textArea) return;

    // Add mobile-specific selection styles
    textArea.style.webkitUserSelect = 'text';
    textArea.style.webkitTouchCallout = 'default';

    // Handle selection change
    document.addEventListener('selectionchange', () => {
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            if (textArea.contains(range.commonAncestorContainer)) {
                updateSelectedText();
            }
        }
    });
}

/**
 * Update selected text and range
 */
function updateSelectedText() {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
        selectedText = selection.toString();
        selectedRange = selection.getRangeAt(0).cloneRange();
        updateButtonStates();
    }
}

/**
 * Set up mobile config panel toggle functionality
 */
function setupMobileConfigToggle() {
    const toggleBtn = document.getElementById('mobile-config-toggle');
    const configPanel = document.getElementById('abbreviated-text-expander-config-panel');

    if (!toggleBtn || !configPanel) return;

    let isConfigOpen = false;

    // Toggle config panel
    toggleBtn.addEventListener('click', () => {
        isConfigOpen = !isConfigOpen;

        if (isConfigOpen) {
            configPanel.classList.add('mobile-open');
            toggleBtn.innerHTML = `
                <span class="toggle-icon">✕</span>
                <span class="toggle-text">Close</span>
            `;
            toggleBtn.style.backgroundColor = '#d32f2f';
        } else {
            configPanel.classList.remove('mobile-open');
            toggleBtn.innerHTML = `
                <span class="toggle-icon">⚙️</span>
                <span class="toggle-text">Tools</span>
            `;
            toggleBtn.style.backgroundColor = 'var(--accent-color)';
        }
    });

    // Close config panel when clicking outside
    configPanel.addEventListener('click', (e) => {
        if (e.target === configPanel && isConfigOpen) {
            isConfigOpen = false;
            configPanel.classList.remove('mobile-open');
            toggleBtn.innerHTML = `
                <span class="toggle-icon">⚙️</span>
                <span class="toggle-text">Tools</span>
            `;
            toggleBtn.style.backgroundColor = 'var(--accent-color)';
        }
    });

    // Close config panel when a button is clicked (mobile workflow)
    const configButtons = configPanel.querySelectorAll('.control-button');
    configButtons.forEach(button => {
        button.addEventListener('click', () => {
            if (isConfigOpen && window.innerWidth <= 768) {
                setTimeout(() => {
                    isConfigOpen = false;
                    configPanel.classList.remove('mobile-open');
                    toggleBtn.innerHTML = `
                        <span class="toggle-icon">⚙️</span>
                        <span class="toggle-text">Tools</span>
                    `;
                    toggleBtn.style.backgroundColor = 'var(--accent-color)';
                }, 300); // Small delay to allow button action to complete
            }
        });
    });
}

/**
 * Set up document handling
 */
function setupDocumentHandling() {
    console.log('Setting up document handling...');
    console.log('DOM elements check:');
    console.log('- abbreviatedTextExpanderFileInput:', !!dom.abbreviatedTextExpanderFileInput);
    console.log('- abbreviatedTextExpanderSaveBtn:', !!dom.abbreviatedTextExpanderSaveBtn);
    console.log('- abbreviatedTextExpanderTextArea:', !!dom.abbreviatedTextExpanderTextArea);

    // Listen for document changes from the main application
    document.addEventListener('documentLoaded', (event) => {
        handleDocumentLoaded(event.detail);
    });

    document.addEventListener('documentClosed', () => {
        handleDocumentClosed();
    });

    // Set up file input handler
    if (dom.abbreviatedTextExpanderFileInput) {
        console.log('Setting up file input handler for:', dom.abbreviatedTextExpanderFileInput);
        dom.abbreviatedTextExpanderFileInput.addEventListener('change', handleFileInput);
    } else {
        console.error('abbreviatedTextExpanderFileInput element not found!');
        console.log('Available DOM elements:', Object.keys(dom));
    }

    // Set up save button handler
    if (dom.abbreviatedTextExpanderSaveBtn) {
        dom.abbreviatedTextExpanderSaveBtn.addEventListener('click', handleSaveDocument);
    }
}

/**
 * Handle document loaded
 */
export function handleDocumentLoaded(document) {
    currentDocument = document;
    console.log('Document loaded in Abbreviated Text Expander:', document?.name);
    
    // Reset history
    documentHistory = [];
    historyIndex = -1;
    
    // Update button states
    updateButtonStates();
    
    // Set up text area if available
    if (dom.abbreviatedTextExpanderTextArea) {
        setupTextAreaHandling();
    }
}

/**
 * Handle document closed
 */
export function handleDocumentClosed() {
    currentDocument = null;
    selectedText = '';
    selectedRange = null;
    documentHistory = [];
    historyIndex = -1;

    console.log('Document closed in Abbreviated Text Expander');
    updateButtonStates();
}

/**
 * Handle file input change
 */
async function handleFileInput(event) {
    console.log('handleFileInput called with event:', event);
    console.log('Files selected:', event.target.files);

    const file = event.target.files[0];
    if (!file) {
        console.log('No file selected');
        return;
    }

    console.log('File selected:', file.name, file.type, file.size);

    if (!file.name.toLowerCase().endsWith('.docx')) {
        updateStatus("Please select a DOCX file.", "error");
        return;
    }

    try {
        updateStatus("Loading document...", "info");

        // Check if mammoth is available
        if (!window.mammoth) {
            throw new Error('Mammoth.js library is not loaded');
        }
        console.log('Mammoth.js is available:', !!window.mammoth);

        // Use mammoth.js to extract text from DOCX
        const arrayBuffer = await file.arrayBuffer();
        console.log('File converted to arrayBuffer, size:', arrayBuffer.byteLength);

        const result = await window.mammoth.extractRawText({ arrayBuffer });

        console.log('Mammoth extraction result:', result);
        console.log('Extracted text length:', result.value ? result.value.length : 0);
        console.log('Mammoth messages:', result.messages);

        if (result.value && result.value.trim().length > 0) {
            // Create document object
            const document = {
                file: file,
                originalText: result.value,
                name: file.name,
                modified: false
            };

            // Load the document
            handleDocumentLoaded(document);

            // Set the text content
            if (dom.abbreviatedTextExpanderTextArea) {
                dom.abbreviatedTextExpanderTextArea.textContent = result.value;
            }

            // Update file info
            if (dom.abbreviatedTextExpanderFileInfo) {
                dom.abbreviatedTextExpanderFileInfo.textContent = `Document loaded: ${file.name}`;
            }

            updateStatus("Document loaded successfully.", "success");
        } else {
            // Try alternative extraction method for html-docx-js generated files
            console.log('Primary extraction failed, trying alternative method...');
            await tryAlternativeExtraction(file);
        }
    } catch (error) {
        console.error('Error loading document:', error);
        updateStatus(`Failed to load document: ${error.message}`, "error");
    }
}

/**
 * Try alternative extraction method for html-docx-js generated files
 */
async function tryAlternativeExtraction(file) {
    try {
        console.log('Attempting alternative extraction...');

        // Try extracting with HTML preservation
        const arrayBuffer = await file.arrayBuffer();
        const htmlResult = await window.mammoth.convertToHtml({ arrayBuffer });

        console.log('HTML extraction result:', htmlResult);

        if (htmlResult.value && htmlResult.value.trim().length > 0) {
            // Convert HTML back to text but preserve some formatting
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlResult.value;

            // Get text content but preserve line breaks
            const textContent = tempDiv.textContent || tempDiv.innerText || '';

            if (textContent.trim().length > 0) {
                // Create document object
                const document = {
                    file: file,
                    originalText: textContent,
                    name: file.name,
                    modified: false
                };

                // Load the document
                handleDocumentLoaded(document);

                // Set the content (preserve HTML if it has formatting)
                if (dom.abbreviatedTextExpanderTextArea) {
                    if (htmlResult.value.includes('<strong>') || htmlResult.value.includes('<b>')) {
                        // Preserve HTML formatting
                        dom.abbreviatedTextExpanderTextArea.innerHTML = htmlResult.value;
                    } else {
                        // Use plain text
                        dom.abbreviatedTextExpanderTextArea.textContent = textContent;
                    }
                }

                // Update file info
                if (dom.abbreviatedTextExpanderFileInfo) {
                    dom.abbreviatedTextExpanderFileInfo.textContent = `Document loaded: ${file.name}`;
                }

                updateStatus("Document loaded successfully (alternative method).", "success");
                return;
            }
        }

        // If all methods fail
        updateStatus("Failed to extract text from document. The file may be corrupted or in an unsupported format.", "error");

    } catch (error) {
        console.error('Alternative extraction failed:', error);
        updateStatus("Failed to extract text from document. The file may be corrupted or in an unsupported format.", "error");
    }
}

/**
 * Handle save document
 */
async function handleSaveDocument() {
    if (!currentDocument) {
        updateStatus("No document loaded.", "error");
        return;
    }

    try {
        updateStatus("Saving document...", "info");

        // Get the current content (preserve HTML formatting)
        const htmlContent = dom.abbreviatedTextExpanderTextArea.innerHTML || dom.abbreviatedTextExpanderTextArea.textContent;

        // Process the content to ensure proper paragraph structure
        let processedContent;
        if (htmlContent.includes('<')) {
            // Content has HTML formatting - preserve it
            processedContent = htmlContent
                .replace(/<div>/gi, '<p>')
                .replace(/<\/div>/gi, '</p>')
                .replace(/<br\s*\/?>/gi, '</p><p>')
                .replace(/\n/g, '</p><p>');

            // Ensure we have proper paragraph tags
            if (!processedContent.startsWith('<p>')) {
                processedContent = '<p>' + processedContent;
            }
            if (!processedContent.endsWith('</p>')) {
                processedContent = processedContent + '</p>';
            }

            // Clean up empty paragraphs and fix double paragraphs
            processedContent = processedContent
                .replace(/<p><\/p>/g, '<p>&nbsp;</p>')
                .replace(/<p>\s*<p>/g, '<p>')
                .replace(/<\/p>\s*<\/p>/g, '</p>');
        } else {
            // Plain text content
            processedContent = htmlContent.split('\n').map(line => `<p>${line || '&nbsp;'}</p>`).join('');
        }



        // Convert HTML to DOCX using the new generator
        const docxBlob = await convertHtmlToDocxBlob(processedContent);

        // Download the file
        downloadBlob(docxBlob, currentDocument.name);

        // Mark as not modified
        currentDocument.modified = false;
        updateButtonStates();

        updateStatus("Document saved successfully.", "success");
    } catch (error) {
        console.error('Error saving document:', error);
        updateStatus(`Failed to save document: ${error.message}`, "error");
    }
}

/**
 * Set up text area handling
 */
function setupTextAreaHandling() {
    if (!dom.abbreviatedTextExpanderTextArea) return;

    // Add event listeners for text selection
    dom.abbreviatedTextExpanderTextArea.addEventListener('mousedown', handleMouseDown);
    dom.abbreviatedTextExpanderTextArea.addEventListener('mouseup', handleMouseUp);
    dom.abbreviatedTextExpanderTextArea.addEventListener('selectstart', handleSelectionStart);
    dom.abbreviatedTextExpanderTextArea.addEventListener('input', handleDocumentChange);

    // Add keyboard selection detection
    dom.abbreviatedTextExpanderTextArea.addEventListener('keyup', handleKeyUp);
    dom.abbreviatedTextExpanderTextArea.addEventListener('selectionchange', handleSelectionChange);

    // Add click handler for auto-selecting paragraphs
    dom.abbreviatedTextExpanderTextArea.addEventListener('click', handleTextAreaClick);

    // Set up toolbar button event listeners
    setupToolbarButtons();

    // Add global selection change listener
    document.addEventListener('selectionchange', () => {
        // Only update if the selection is within our text area
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            if (dom.abbreviatedTextExpanderTextArea &&
                dom.abbreviatedTextExpanderTextArea.contains(range.commonAncestorContainer)) {
                setTimeout(() => {
                    updateSelectedText();
                }, 10);
            }
        }
    });
}

/**
 * Set up toolbar button event listeners
 */
function setupToolbarButtons() {
    if (dom.selectAllTextBtn) {
        dom.selectAllTextBtn.addEventListener('click', handleSelectAll);
    }

    if (dom.clearSelectionBtn) {
        dom.clearSelectionBtn.addEventListener('click', handleClearSelection);
    }

    if (dom.undoChangesBtn) {
        dom.undoChangesBtn.addEventListener('click', handleUndo);
    }

    if (dom.redoChangesBtn) {
        dom.redoChangesBtn.addEventListener('click', handleRedo);
    }
}

/**
 * Handle mouse down - start of selection
 */
function handleMouseDown(event) {
    isSelecting = true;
    selectionStartPos = getCaretPosition(event);
}

/**
 * Handle mouse up - end of selection
 */
function handleMouseUp() {
    if (isSelecting) {
        isSelecting = false;
        selectionStartPos = null;

        // Add a small delay to ensure selection is complete
        setTimeout(() => {
            updateSelectedText();
        }, 10);
    }
}

/**
 * Handle selection start event
 */
function handleSelectionStart() {
    // Allow text selection to proceed normally
    return true;
}

/**
 * Handle key up - for keyboard selection
 */
function handleKeyUp(event) {
    // Check for selection-related keys
    if (event.shiftKey || event.key === 'ArrowLeft' || event.key === 'ArrowRight' ||
        event.key === 'ArrowUp' || event.key === 'ArrowDown' ||
        event.key === 'Home' || event.key === 'End' ||
        event.ctrlKey && event.key === 'a') {
        setTimeout(() => {
            updateSelectedText();
        }, 10);
    }
}

/**
 * Handle selection change event
 */
function handleSelectionChange() {
    setTimeout(() => {
        updateSelectedText();
    }, 10);
}

/**
 * Handle text area click - auto-select paragraph if no text is selected
 */
function handleTextAreaClick(event) {
    // Small delay to let any existing selection settle
    setTimeout(() => {
        const selection = window.getSelection();

        // Only auto-select if no text is currently selected
        if (!selection.toString().trim()) {
            console.log('Auto-selecting paragraph from click...');

            // Try to select paragraph using text-based approach
            const range = document.caretRangeFromPoint(event.clientX, event.clientY);

            if (range) {
                const textNode = range.startContainer;
                console.log('Clicked node type:', textNode.nodeType, 'Content preview:', textNode.textContent?.substring(0, 50));

                if (textNode.nodeType === Node.TEXT_NODE) {
                    // Get the full text content of the entire text area
                    const fullText = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
                    const clickOffset = getAbsoluteOffset(textNode, range.startOffset);

                    console.log('Click offset in full text:', clickOffset);

                    // Find paragraph boundaries using double line breaks or significant spacing
                    const paragraphBoundaries = findParagraphBoundaries(fullText);
                    const currentParagraph = findCurrentParagraph(clickOffset, paragraphBoundaries);

                    if (currentParagraph) {
                        console.log('Found paragraph:', currentParagraph.text.substring(0, 100) + '...');
                        selectTextRange(currentParagraph.start, currentParagraph.end);
                    } else {
                        console.log('No paragraph found, selecting sentence');
                        selectCurrentSentence(event);
                    }
                } else {
                    console.log('Not a text node, trying sentence selection');
                    selectCurrentSentence(event);
                }
            }
        }
    }, 50);
}

/**
 * Get absolute offset of a text node position within the entire text area
 */
function getAbsoluteOffset(textNode, relativeOffset) {
    let offset = 0;
    const walker = document.createTreeWalker(
        dom.abbreviatedTextExpanderTextArea,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    let currentNode;
    while (currentNode = walker.nextNode()) {
        if (currentNode === textNode) {
            return offset + relativeOffset;
        }
        offset += currentNode.textContent.length;
    }

    return offset;
}

/**
 * Find paragraph boundaries in text using multiple strategies
 */
function findParagraphBoundaries(text) {
    const boundaries = [0]; // Start with beginning of text

    // Strategy 1: Look for double line breaks (most common)
    const doubleBreaks = text.matchAll(/\n\s*\n/g);
    for (const match of doubleBreaks) {
        boundaries.push(match.index + match[0].length);
    }

    // Strategy 2: Look for significant indentation or spacing patterns
    const lines = text.split('\n');
    let currentPos = 0;

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const nextLine = lines[i + 1];

        // Check if this looks like a paragraph break
        if (nextLine !== undefined) {
            const currentLineEndsWithPeriod = line.trim().endsWith('.');
            const nextLineStartsCapital = /^[A-Z]/.test(nextLine.trim());
            const significantGap = line.trim() === '' || nextLine.trim() === '';

            if ((currentLineEndsWithPeriod && nextLineStartsCapital) || significantGap) {
                const boundaryPos = currentPos + line.length + 1; // +1 for newline
                if (!boundaries.includes(boundaryPos)) {
                    boundaries.push(boundaryPos);
                }
            }
        }

        currentPos += line.length + 1; // +1 for newline
    }

    boundaries.push(text.length); // End with end of text
    boundaries.sort((a, b) => a - b);

    console.log('Found paragraph boundaries:', boundaries);
    return boundaries;
}

/**
 * Find which paragraph contains the given offset
 */
function findCurrentParagraph(offset, boundaries) {
    for (let i = 0; i < boundaries.length - 1; i++) {
        const start = boundaries[i];
        const end = boundaries[i + 1];

        if (offset >= start && offset < end) {
            const fullText = dom.abbreviatedTextExpanderTextArea.textContent || dom.abbreviatedTextExpanderTextArea.innerText;
            const paragraphText = fullText.substring(start, end).trim();

            // Only return if it's a substantial paragraph (more than just whitespace)
            if (paragraphText.length > 10) {
                return {
                    start: start,
                    end: end,
                    text: paragraphText
                };
            }
        }
    }

    return null;
}

/**
 * Select text range by absolute character positions
 */
function selectTextRange(startOffset, endOffset) {
    const selection = window.getSelection();
    const range = document.createRange();

    let currentOffset = 0;
    let startNode = null, startPos = 0;
    let endNode = null, endPos = 0;

    const walker = document.createTreeWalker(
        dom.abbreviatedTextExpanderTextArea,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    let textNode;
    while (textNode = walker.nextNode()) {
        const nodeLength = textNode.textContent.length;

        // Find start position
        if (!startNode && currentOffset + nodeLength >= startOffset) {
            startNode = textNode;
            startPos = startOffset - currentOffset;
        }

        // Find end position
        if (!endNode && currentOffset + nodeLength >= endOffset) {
            endNode = textNode;
            endPos = endOffset - currentOffset;
            break;
        }

        currentOffset += nodeLength;
    }

    if (startNode && endNode) {
        range.setStart(startNode, Math.max(0, startPos));
        range.setEnd(endNode, Math.min(endNode.textContent.length, endPos));

        selection.removeAllRanges();
        selection.addRange(range);

        updateSelectedText();

        console.log('Selected text range:', selection.toString().substring(0, 100) + '...');
    }
}

/**
 * Select the current sentence around the click position (fallback)
 */
function selectCurrentSentence(event) {
    const selection = window.getSelection();
    const range = document.caretRangeFromPoint(event.clientX, event.clientY);

    if (range) {
        const textNode = range.startContainer;
        if (textNode.nodeType === Node.TEXT_NODE) {
            const text = textNode.textContent;
            const clickOffset = range.startOffset;

            // Find sentence boundaries (simple approach)
            let start = 0;
            let end = text.length;

            // Find start of sentence (look backwards for . ! ? or start of text)
            for (let i = clickOffset - 1; i >= 0; i--) {
                if (text[i].match(/[.!?]/)) {
                    start = i + 1;
                    break;
                }
            }

            // Find end of sentence (look forwards for . ! ?)
            for (let i = clickOffset; i < text.length; i++) {
                if (text[i].match(/[.!?]/)) {
                    end = i + 1;
                    break;
                }
            }

            // Create selection for the sentence
            const sentenceRange = document.createRange();
            sentenceRange.setStart(textNode, start);
            sentenceRange.setEnd(textNode, end);

            selection.removeAllRanges();
            selection.addRange(sentenceRange);

            updateSelectedText();

            console.log('Auto-selected sentence:', selection.toString().trim());
        }
    }
}

/**
 * Get caret position from mouse event
 */
function getCaretPosition(event) {
    if (document.caretRangeFromPoint) {
        const range = document.caretRangeFromPoint(event.clientX, event.clientY);
        return range;
    } else if (document.caretPositionFromPoint) {
        const pos = document.caretPositionFromPoint(event.clientX, event.clientY);
        if (pos) {
            const range = document.createRange();
            range.setStart(pos.offsetNode, pos.offset);
            return range;
        }
    }
    return null;
}

/**
 * Update selected text
 */
function updateSelectedText() {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
        selectedRange = selection.getRangeAt(0);
        selectedText = selectedRange.toString().trim();

        console.log('Selected text:', selectedText ? `"${selectedText}"` : 'none');
        console.log('Selection range:', selectedRange);
        console.log('Selection range collapsed:', selectedRange.collapsed);

        // If no text selected but we have a range, try to get text from the range
        if (!selectedText && selectedRange && !selectedRange.collapsed) {
            try {
                const contents = selectedRange.cloneContents();
                selectedText = contents.textContent?.trim() || '';
                console.log('Text from range contents:', selectedText);
            } catch (e) {
                console.log('Error getting text from range:', e);
            }
        }

        updateButtonStates();

        // Selection display functionality removed as per user preference
    } else {
        // No selection range, clear everything
        selectedText = '';
        selectedRange = null;
        console.log('No selection range found');
        updateButtonStates();


    }
}



/**
 * Handle document content changes
 */
export function handleDocumentChange() {
    if (!currentDocument) return;
    
    // Add to history for undo functionality
    const currentContent = dom.abbreviatedTextExpanderTextArea?.textContent || '';
    
    // Only add to history if content actually changed
    if (documentHistory.length === 0 || documentHistory[documentHistory.length - 1] !== currentContent) {
        // Remove any history after current position (for redo functionality)
        documentHistory = documentHistory.slice(0, historyIndex + 1);
        
        // Add new state
        documentHistory.push(currentContent);
        historyIndex = documentHistory.length - 1;
        
        // Limit history size
        if (documentHistory.length > 50) {
            documentHistory.shift();
            historyIndex--;
        }
    }
    
    // Mark document as modified
    if (currentDocument) {
        currentDocument.modified = true;
    }
}

/**
 * Update button states based on current state
 */
export function updateButtonStates() {
    const hasDocument = !!currentDocument;
    const hasSelection = selectedText.length > 0;
    const hasApiKey = geminiApiKey.length > 0;

    console.log('Button state update:', {
        hasDocument,
        hasSelection,
        hasApiKey,
        selectedTextLength: selectedText.length,
        geminiApiKeyLength: geminiApiKey.length,
        currentDocumentName: currentDocument?.name
    });

    // AI expansion button (requires document, selection, and API key)
    if (dom.expandAbbreviatedTextBtn) {
        const shouldEnable = hasDocument && hasSelection && hasApiKey;
        dom.expandAbbreviatedTextBtn.disabled = !shouldEnable;
        console.log('Expand abbreviations button disabled:', !shouldEnable);
    }
    
    // Test connection button removed from UI since API key is hardcoded
    
    // Highlight abbreviations button
    if (dom.highlightAbbreviationsBtn) {
        dom.highlightAbbreviationsBtn.disabled = !hasDocument;
    }
    
    // Scripture formatting button
    if (dom.formatScriptureBtn) {
        dom.formatScriptureBtn.disabled = !hasDocument;
    }
    
    // Quote formatting button
    if (dom.formatQuotesBtn) {
        dom.formatQuotesBtn.disabled = !hasDocument;
    }
    
    // Abbreviation expansion button
    if (dom.expandAbbreviationsBtn) {
        dom.expandAbbreviationsBtn.disabled = !hasDocument;
    }
    
    // Papal saints formatting button
    if (dom.formatPapalSaintsBtn) {
        dom.formatPapalSaintsBtn.disabled = !hasDocument;
    }
    
    // Footnotes formatting button
    if (dom.formatFootnotesBtn) {
        dom.formatFootnotesBtn.disabled = !hasDocument;
    }
    
    // Run all formatting button (requires document, selection, and API key since it uses AI)
    if (dom.runAllFormattingBtn) {
        dom.runAllFormattingBtn.disabled = !hasDocument || !hasSelection || !hasApiKey;
    }

    // Save button (requires document)
    if (dom.abbreviatedTextExpanderSaveBtn) {
        dom.abbreviatedTextExpanderSaveBtn.disabled = !hasDocument;
    }

    // Select All button (requires document)
    if (dom.selectAllTextBtn) {
        const shouldDisable = !hasDocument;
        dom.selectAllTextBtn.disabled = shouldDisable;
        console.log('Select All button disabled:', shouldDisable, '(hasDocument:', hasDocument, ')');
    } else {
        console.warn('Select All button element not found!');
    }

    // Clear Selection button (requires document and selection)
    if (dom.clearSelectionBtn) {
        const shouldDisable = !hasDocument || !hasSelection;
        dom.clearSelectionBtn.disabled = shouldDisable;
        console.log('Clear Selection button disabled:', shouldDisable, '(hasDocument:', hasDocument, ', hasSelection:', hasSelection, ')');
    } else {
        console.warn('Clear Selection button element not found!');
    }

    // Undo/Redo buttons
    if (dom.undoChangesBtn) {
        const shouldDisableUndo = !hasDocument || historyIndex <= 0;
        dom.undoChangesBtn.disabled = shouldDisableUndo;
        console.log('Undo button disabled:', shouldDisableUndo, '(hasDocument:', hasDocument, ', historyIndex:', historyIndex, ')');
    } else {
        console.warn('Undo button element not found!');
    }

    if (dom.redoChangesBtn) {
        const shouldDisableRedo = !hasDocument || historyIndex >= documentHistory.length - 1;
        dom.redoChangesBtn.disabled = shouldDisableRedo;
        console.log('Redo button disabled:', shouldDisableRedo, '(hasDocument:', hasDocument, ', historyIndex:', historyIndex, ', historyLength:', documentHistory.length, ')');
    } else {
        console.warn('Redo button element not found!');
    }
}

/**
 * Update instructions preview
 */
export function updateInstructionsPreview() {
    // This will be implemented in the AI module
    // For now, just a placeholder
    if (dom.currentInstructionsPreview) {
        dom.currentInstructionsPreview.innerHTML = '<p>Instructions preview will be updated by AI module</p>';
    }
}

/**
 * Update status message
 */
export function updateStatus(message, type = 'info') {
    console.log(`Status (${type}):`, message);

    // Debug: Check if status bar element exists
    console.log('Status bar element found:', !!dom.abbreviatedTextExpanderStatusBar);
    if (dom.abbreviatedTextExpanderStatusBar) {
        console.log('Status bar current text:', dom.abbreviatedTextExpanderStatusBar.textContent);
        console.log('Setting status bar to:', message);
    }

    // Update status in the abbreviated text expander status bar
    if (dom.abbreviatedTextExpanderStatusBar) {
        dom.abbreviatedTextExpanderStatusBar.textContent = message;
        dom.abbreviatedTextExpanderStatusBar.className = `status-bar ${type}`;

        // Add visual styling based on type
        dom.abbreviatedTextExpanderStatusBar.style.display = 'block';
        dom.abbreviatedTextExpanderStatusBar.style.visibility = 'visible';

        // Auto-hide after 5 seconds for non-error messages
        if (type !== 'error') {
            setTimeout(() => {
                dom.abbreviatedTextExpanderStatusBar.textContent = 'Abbreviated Text Expander Ready.';
                dom.abbreviatedTextExpanderStatusBar.className = 'status-bar';
            }, 5000);
        }
    } else {
        console.warn('Status bar element not found! Cannot display status message:', message);
    }
}

// Export state setters for other modules
export function setCurrentDocument(doc) { currentDocument = doc; }
export function setSelectedText(text) { selectedText = text; }
export function setSelectedRange(range) { selectedRange = range; }
export function setGeminiApiKey(key) { geminiApiKey = key; }
export function addToHistory(content) { 
    documentHistory.push(content);
    historyIndex = documentHistory.length - 1;
}

/**
 * Handle select all button
 */
function handleSelectAll() {
    if (!dom.abbreviatedTextExpanderTextArea) return;

    // Select all text in the editor
    const range = document.createRange();
    range.selectNodeContents(dom.abbreviatedTextExpanderTextArea);

    const selection = window.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);

    // Update selected text
    updateSelectedText();

    console.log('Selected all text');
}

/**
 * Handle clear selection button
 */
function handleClearSelection() {
    // Clear the selection
    const selection = window.getSelection();
    selection.removeAllRanges();

    // Reset selected text state
    selectedText = '';
    selectedRange = null;

    // Update button states
    updateButtonStates();

    console.log('Cleared text selection');
}

/**
 * Handle undo button
 */
function handleUndo() {
    if (historyIndex > 0) {
        historyIndex--;
        const previousContent = documentHistory[historyIndex];

        if (dom.abbreviatedTextExpanderTextArea) {
            dom.abbreviatedTextExpanderTextArea.textContent = previousContent;
        }

        // Update button states
        updateButtonStates();

        console.log('Undo applied, history index:', historyIndex);
    }
}

/**
 * Handle redo button
 */
function handleRedo() {
    if (historyIndex < documentHistory.length - 1) {
        historyIndex++;
        const nextContent = documentHistory[historyIndex];

        if (dom.abbreviatedTextExpanderTextArea) {
            dom.abbreviatedTextExpanderTextArea.textContent = nextContent;
        }

        // Update button states
        updateButtonStates();

        console.log('Redo applied, history index:', historyIndex);
    }
}

// Export state getters for other modules
export function getCurrentDocument() { return currentDocument; }
export function getSelectedText() {
    // Always try to refresh selection before returning
    refreshSelection();
    return selectedText;
}
export function getSelectedRange() { return selectedRange; }
export function getGeminiApiKey() { return geminiApiKey; }
export function getDocumentHistory() { return documentHistory; }
export function getHistoryIndex() { return historyIndex; }

/**
 * Refresh the current selection
 */
export function refreshSelection() {
    updateSelectedText();
}
