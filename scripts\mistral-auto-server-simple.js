#!/usr/bin/env node

/**
 * Simple Mistral Auto-Start Server
 * Focuses on mock server functionality with offline setup guidance
 */

import express from 'express';
import { spawn, exec } from 'child_process';
import cors from 'cors';

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.')); // Serve static files

// Store running processes
const runningProcesses = new Map();

/**
 * Check system requirements for offline Mistral deployment
 */
app.get('/api/system/requirements', (req, res) => {
    const requirements = {
        python: false,
        transformers: false,
        torch: false,
        recommendations: []
    };
    
    // Check Python
    exec('python --version', (pythonError, pythonStdout) => {
        requirements.python = !pythonError;
        if (requirements.python) {
            requirements.pythonVersion = pythonStdout.trim();
        }

        // Check Transformers installation
        exec('python -c "import transformers; print(transformers.__version__)"', (transformersError, transformersStdout) => {
            requirements.transformers = !transformersError;
            if (requirements.transformers) {
                requirements.transformersVersion = transformersStdout.trim();
            }
            
            // Check PyTorch installation
            exec('python -c "import torch; print(torch.__version__)"', (torchError, torchStdout) => {
                requirements.torch = !torchError;
                if (requirements.torch) {
                    requirements.torchVersion = torchStdout.trim();
                }

                // Generate recommendations
                if (!requirements.python) {
                    requirements.recommendations.push({
                        type: 'critical',
                        message: 'Install Python 3.8 or higher',
                        action: 'Download from https://python.org/downloads/'
                    });
                }
                
                if (!requirements.transformers) {
                    requirements.recommendations.push({
                        type: 'warning',
                        message: 'Transformers not installed',
                        action: 'Run: pip install transformers accelerate fastapi uvicorn pillow'
                    });
                }
                
                if (!requirements.torch) {
                    requirements.recommendations.push({
                        type: 'warning',
                        message: 'PyTorch not installed',
                        action: 'Run: pip install torch torchvision'
                    });
                }
                
                requirements.recommendations.push({
                    type: 'info',
                    message: 'Pixtral-12B requires ~24GB disk space and 8GB+ RAM',
                    action: 'Ensure sufficient system resources before deployment'
                });

                requirements.recommendations.push({
                    type: 'info',
                    message: 'HuggingFace token required for model access',
                    action: 'Get token from https://huggingface.co/settings/tokens and accept license at https://huggingface.co/mistralai/Pixtral-12B-2409'
                });

                requirements.recommendations.push({
                    type: 'success',
                    message: 'Windows-compatible Transformers approach available',
                    action: 'No Docker or vLLM required - uses Transformers + PyTorch directly'
                });
                
                res.json(requirements);
            });
        });
    });
});

/**
 * Setup HuggingFace authentication
 */
app.post('/api/huggingface/setup', (req, res) => {
    const { token } = req.body;
    
    if (!token) {
        return res.status(400).json({
            success: false,
            error: 'HuggingFace token is required'
        });
    }
    
    // Set environment variable for this session
    process.env.HUGGING_FACE_HUB_TOKEN = token;
    process.env.HF_TOKEN = token;
    
    res.json({
        success: true,
        message: 'HuggingFace token stored for this session',
        note: 'For permanent setup, run: huggingface-cli login --token YOUR_TOKEN'
    });
});

/**
 * Check if model is downloaded for offline use
 */
app.get('/api/mistral/check-offline', (req, res) => {
    exec('python mistral-offline-setup.py --check', (error, stdout, stderr) => {
        const isAvailable = stdout.includes('Model ready for offline use');
        res.json({
            offline_available: isAvailable,
            output: stdout,
            error: error ? error.message : null
        });
    });
});

/**
 * Download model for offline use
 */
app.post('/api/mistral/download-model', (req, res) => {
    const { hfToken } = req.body;

    if (!hfToken) {
        return res.status(400).json({
            success: false,
            error: 'HuggingFace token is required for model download',
            instructions: [
                '1. Get HuggingFace token from https://huggingface.co/settings/tokens',
                '2. Accept license at https://huggingface.co/mistralai/Pixtral-12B-2409',
                '3. Enter your token and click "Download Model"'
            ]
        });
    }

    // Start model download
    downloadModelForOffline(hfToken, res);
});

/**
 * Start offline Mistral server (no token needed if model downloaded)
 */
app.post('/api/mistral/start-offline', (req, res) => {
    const { port = '8000' } = req.body;

    // Check if model is available offline first
    exec('python mistral-offline-setup.py --check', (error, stdout, stderr) => {
        const isAvailable = stdout.includes('Model ready for offline use');

        if (!isAvailable) {
            return res.status(400).json({
                success: false,
                error: 'Model not downloaded yet',
                instructions: [
                    '1. Get HuggingFace token from https://huggingface.co/settings/tokens',
                    '2. Accept license at https://huggingface.co/mistralai/Pixtral-12B-2409',
                    '3. Click "Download Model" first',
                    '4. Then start offline server'
                ],
                needsDownload: true
            });
        }

        // Start the offline server
        startOfflineServer(port, res);
    });
});

/**
 * Check if Ollama is available
 */
app.get('/api/ollama/check', (req, res) => {
    // Check both command line and API availability
    exec('ollama --version', (cmdError, cmdStdout) => {
        const cmdAvailable = !cmdError;

        // Also check API availability
        fetch('http://localhost:11434/api/version')
            .then(response => response.json())
            .then(apiData => {
                res.json({
                    ollama_available: true,
                    command_available: cmdAvailable,
                    api_available: true,
                    version: cmdAvailable ? cmdStdout.trim() : apiData.version,
                    api_version: apiData.version,
                    note: cmdAvailable ? 'Full Ollama installation' : 'Ollama service running (command not in PATH)'
                });
            })
            .catch(apiError => {
                res.json({
                    ollama_available: cmdAvailable,
                    command_available: cmdAvailable,
                    api_available: false,
                    version: cmdAvailable ? cmdStdout.trim() : null,
                    error: cmdAvailable ? null : 'Ollama not installed or running'
                });
            });
    });
});

/**
 * Start Ollama vision model
 */
app.post('/api/ollama/start', (req, res) => {
    const { model = 'llava:7b', port = '11434' } = req.body;

    // Check if Ollama is available
    exec('ollama --version', (error) => {
        if (error) {
            return res.status(400).json({
                success: false,
                error: 'Ollama not installed',
                instructions: [
                    'Download Ollama from https://ollama.ai/download',
                    'Install and restart',
                    'Run: ollama pull llava:7b',
                    'Then try again'
                ]
            });
        }

        // Start Ollama model
        startOllamaModel(model, res);
    });
});

/**
 * Start mock Mistral server
 */
app.post('/api/mistral/start-mock', (req, res) => {
    const { port = '8000' } = req.body;

    // Check if port is already in use
    const mockServer = createMockServer(parseInt(port));

    mockServer.listen(parseInt(port), (err) => {
        if (err) {
            res.status(500).json({
                success: false,
                error: `Port ${port} is already in use or unavailable`
            });
        } else {
            runningProcesses.set(`mock-${port}`, mockServer);
            res.json({
                success: true,
                message: `Enhanced mock Mistral server started on port ${port}`,
                type: 'mock',
                note: 'Provides realistic page-specific responses for testing'
            });
        }
    });
});

/**
 * Create an enhanced mock Mistral server
 */
function createMockServer(port) {
    const mockApp = express();
    mockApp.use(cors());
    mockApp.use(express.json({ limit: '50mb' }));
    
    // Models endpoint
    mockApp.get('/v1/models', (req, res) => {
        res.json({
            object: "list",
            data: [
                {
                    id: "mistralai/Pixtral-12B-2409",
                    object: "model",
                    created: Date.now(),
                    owned_by: "mistral-mock"
                }
            ]
        });
    });
    
    // Chat completions endpoint (enhanced mock vision processing)
    mockApp.post('/v1/chat/completions', (req, res) => {
        const { messages, model } = req.body;
        
        // Simulate processing delay
        setTimeout(() => {
            // Try to extract meaningful content from the request
            let extractedText = generateSmartMockText(messages);
            
            res.json({
                id: `chatcmpl-${Date.now()}`,
                object: "chat.completion",
                created: Math.floor(Date.now() / 1000),
                model: model || "mistralai/Pixtral-12B-2409",
                choices: [
                    {
                        index: 0,
                        message: {
                            role: "assistant",
                            content: extractedText
                        },
                        finish_reason: "stop"
                    }
                ],
                usage: {
                    prompt_tokens: 100,
                    completion_tokens: extractedText.length / 4,
                    total_tokens: 150 + (extractedText.length / 4)
                }
            });
        }, 500 + Math.random() * 1500); // 0.5-2 second delay
    });
    
    // Helper function to generate smarter mock text
    function generateSmartMockText(messages) {
        // Look for page information in the messages
        let pageNumber = 1;
        
        if (messages && messages.length > 0) {
            const lastMessage = messages[messages.length - 1];
            if (lastMessage.content) {
                // Handle both string and array content
                let contentText = '';
                if (Array.isArray(lastMessage.content)) {
                    // Find text content in multimodal message
                    const textPart = lastMessage.content.find(part => part.type === 'text');
                    contentText = textPart ? textPart.text : '';
                } else {
                    contentText = lastMessage.content;
                }
                
                // Try to extract page number from the prompt
                const pageMatch = contentText.match(/page\s*(\d+)/i);
                if (pageMatch) {
                    pageNumber = parseInt(pageMatch[1]);
                }
            }
        }
        
        // Generate different content based on page number
        const mockContent = generatePageContent(pageNumber);
        
        return `${mockContent}\n\n[MOCK EXTRACTION] This is simulated text extraction. For real OCR, install vLLM with Mistral Pixtral-12B.`;
    }
    
    function generatePageContent(pageNumber) {
        if (pageNumber === 1) {
            return "ST. ANTHONY\nTHE WONDER WORKER\n\nA Biography\n\nBy [Author Name]\n\nPublished by [Publisher]\n[Year]";
        } else if (pageNumber === 2) {
            return "TABLE OF CONTENTS\n\nChapter 1: Early Life ........................... 3\nChapter 2: Religious Calling ................... 15\nChapter 3: Miracles and Wonders ................ 28\nChapter 4: Legacy and Canonization ............. 45\nAppendix: Prayers and Devotions ................ 62";
        } else {
            // Generate chapter content with variations
            let baseContent = `Chapter ${Math.ceil((pageNumber - 2) / 8)}\n\nSaint Anthony of Padua, born Fernando Martins de Bulhões in Lisbon around 1195, emerged as one of the most beloved figures in Christian history. His journey from a privileged Portuguese family to becoming a revered Franciscan friar exemplifies the transformative power of faith and dedication.`;
            
            // Add page-specific variations
            if (pageNumber % 3 === 0) {
                baseContent += "\n\nMany pilgrims traveled great distances to hear Anthony preach. His words had the power to convert even the most hardened hearts, and numerous miracles were attributed to his intercession.";
            } else if (pageNumber % 3 === 1) {
                baseContent += "\n\nThe Franciscan order provided Anthony with the perfect vehicle for his ministry. He embraced the ideals of poverty, humility, and service to others that characterized the followers of Saint Francis.";
            } else {
                baseContent += "\n\nAnthony's theological knowledge was vast, but he never let learning become an end in itself. Instead, he used his education to serve God and help others grow in faith and understanding.";
            }
            
            return baseContent;
        }
    }
    
    return mockApp;
}

/**
 * Download model for offline use
 */
function downloadModelForOffline(hfToken, res) {
    try {
        console.log('Starting model download for offline use...');

        // Start the download process
        const downloadProcess = spawn('python', ['mistral-offline-setup.py', '--download', hfToken], {
            stdio: 'pipe',
            shell: true
        });

        let downloadOutput = '';

        downloadProcess.stdout.on('data', (data) => {
            const output = data.toString();
            downloadOutput += output;
            console.log('Download:', output);
        });

        downloadProcess.stderr.on('data', (data) => {
            const output = data.toString();
            downloadOutput += output;
            console.log('Download Error:', output);
        });

        downloadProcess.on('close', (code) => {
            console.log(`Download process exited with code ${code}`);
            runningProcesses.delete('model-download');
        });

        // Store the process
        runningProcesses.set('model-download', downloadProcess);

        // Respond immediately
        res.json({
            success: true,
            message: 'Model download started',
            type: 'download',
            note: 'Downloading 24GB model. This will take 15-30 minutes. Check logs for progress.',
            model: 'mistralai/Pixtral-12B-2409'
        });

    } catch (error) {
        res.status(500).json({
            success: false,
            error: `Failed to start download: ${error.message}`
        });
    }
}

/**
 * Start Ollama model
 */
function startOllamaModel(model, res) {
    try {
        console.log(`Starting Ollama model: ${model}...`);

        // First, try to pull the model if not available
        const pullProcess = spawn('ollama', ['pull', model], {
            stdio: 'pipe',
            shell: true
        });

        let pullOutput = '';

        pullProcess.stdout.on('data', (data) => {
            const output = data.toString();
            pullOutput += output;
            console.log('Ollama Pull:', output);
        });

        pullProcess.stderr.on('data', (data) => {
            const output = data.toString();
            pullOutput += output;
            console.log('Ollama Pull Error:', output);
        });

        pullProcess.on('close', (code) => {
            console.log(`Ollama pull process exited with code ${code}`);

            // Respond with success
            res.json({
                success: true,
                message: `Ollama ${model} ready for use`,
                type: 'ollama',
                model: model,
                note: 'No authentication required! Use Ollama API on port 11434',
                api_endpoint: 'http://localhost:11434/api/generate'
            });
        });

        // Store the process
        runningProcesses.set(`ollama-${model}`, pullProcess);

    } catch (error) {
        res.status(500).json({
            success: false,
            error: `Failed to start Ollama: ${error.message}`
        });
    }
}

/**
 * Start offline Mistral server (no internet required)
 */
function startOfflineServer(port, res) {
    try {
        console.log('Starting offline Mistral server...');

        // Start the offline server
        const serverProcess = spawn('python', ['mistral-offline-server.py', '--port', port], {
            stdio: 'pipe',
            shell: true
        });

        let serverOutput = '';

        serverProcess.stdout.on('data', (data) => {
            const output = data.toString();
            serverOutput += output;
            console.log('Offline Server:', output);
        });

        serverProcess.stderr.on('data', (data) => {
            const output = data.toString();
            serverOutput += output;
            console.log('Offline Server Error:', output);
        });

        serverProcess.on('close', (code) => {
            console.log(`Offline server process exited with code ${code}`);
            runningProcesses.delete(`offline-${port}`);
        });

        // Store the process
        runningProcesses.set(`offline-${port}`, serverProcess);

        // Give the server a moment to start
        setTimeout(() => {
            res.json({
                success: true,
                message: `Offline Mistral AI server starting on port ${port}`,
                type: 'offline',
                port: port,
                note: 'True offline mode - no internet or tokens required for inference!',
                model: 'mistralai/Pixtral-12B-2409'
            });
        }, 1000);

    } catch (error) {
        res.status(500).json({
            success: false,
            error: `Failed to start offline server: ${error.message}`
        });
    }
}

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        service: 'mistral-auto-server-simple',
        timestamp: new Date().toISOString()
    });
});

// Start the server
app.listen(PORT, () => {
    console.log(`🚀 Mistral Auto-Start Server running on port ${PORT}`);
    console.log(`📋 Available endpoints:`);
    console.log(`   GET  /api/system/requirements - Check system requirements`);
    console.log(`   POST /api/huggingface/setup - Setup HuggingFace token`);
    console.log(`   GET  /api/ollama/check - Check if Ollama available`);
    console.log(`   POST /api/ollama/start - Start Ollama vision model (no auth!)`);
    console.log(`   GET  /api/mistral/check-offline - Check if model downloaded`);
    console.log(`   POST /api/mistral/download-model - Download model for offline use`);
    console.log(`   POST /api/mistral/start-offline - Start offline server (no token needed)`);
    console.log(`   POST /api/mistral/start-mock - Start enhanced mock server`);
    console.log(`   GET  /health - Health check`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down Mistral Auto-Start Server...');
    
    // Stop all mock servers
    runningProcesses.forEach((server, key) => {
        if (key.startsWith('mock-')) {
            server.close();
        }
    });
    
    process.exit(0);
});
