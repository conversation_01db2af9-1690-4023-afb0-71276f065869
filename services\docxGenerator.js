import { Document, Packer, Paragraph, TextRun } from 'docx';

/**
 * Parses a DOM node and its children to create an array of docx TextRun objects.
 * This handles basic formatting like bold and italics.
 * @param {Node} node The DOM node to parse.
 * @param {Object} inheritedFormatting Formatting inherited from parent elements.
 * @returns {TextRun[]} An array of TextRun objects.
 */
function parseNodeToTextRuns(node, inheritedFormatting = {}) {
  const runs = [];

  for (const child of node.childNodes) {
    if (child.nodeType === Node.TEXT_NODE) {
      const text = child.textContent;
      if (text.trim()) { // Only add non-empty text
        runs.push(new TextRun({
          text: text,
          ...inheritedFormatting
        }));
      }
    } else if (child.nodeType === Node.ELEMENT_NODE) {
      // Determine formatting for this element
      const formatting = { ...inheritedFormatting };

      if (child.nodeName === 'B' || child.nodeName === 'STRONG') {
        formatting.bold = true;
      }
      if (child.nodeName === 'I' || child.nodeName === 'EM') {
        formatting.italics = true;
      }
      if (child.nodeName === 'U') {
        formatting.underline = {};
      }

      // Recurse for nested elements, passing down formatting
      const childRuns = parseNodeToTextRuns(child, formatting);
      runs.push(...childRuns);
    }
  }

  return runs;
}

/**
 * Converts an HTML string into a `docx` Document object that can be packed into a .docx file.
 * @param {string} htmlString The HTML content from the editor.
 * @returns {Promise<Blob>} A promise that resolves with the .docx file as a Blob.
 */
export async function convertHtmlToDocxBlob(htmlString) {
  const parser = new DOMParser();
  // Wrap in a div to ensure proper parsing of top-level nodes
  const doc = parser.parseFromString(`<div id="wrapper">${htmlString}</div>`, 'text/html');

  const paragraphs = [];
  const wrapper = doc.getElementById('wrapper');

  if (!wrapper) {
    // Fallback if wrapper not found
    paragraphs.push(new Paragraph({
      children: [new TextRun({ text: htmlString })]
    }));
  } else {
    const nodes = wrapper.childNodes;

    for (const node of nodes) {
      if (node.nodeType === Node.TEXT_NODE) {
        // Handle top-level text nodes
        const text = node.textContent.trim();
        if (text) {
          paragraphs.push(new Paragraph({
            children: [new TextRun({ text })]
          }));
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        // Handle different HTML elements
        if (node.tagName === 'P' || node.tagName === 'DIV') {
          const children = parseNodeToTextRuns(node);
          if (children.length === 0) {
            // Empty paragraph
            paragraphs.push(new Paragraph({
              children: [new TextRun({ text: '' })]
            }));
          } else {
            paragraphs.push(new Paragraph({ children }));
          }
        } else if (node.tagName === 'BR') {
          // Line break creates empty paragraph
          paragraphs.push(new Paragraph({
            children: [new TextRun({ text: '' })]
          }));
        } else {
          // Other elements - treat as inline content in a paragraph
          const children = parseNodeToTextRuns(node);
          if (children.length > 0) {
            paragraphs.push(new Paragraph({ children }));
          }
        }
      }
    }
  }

  // Ensure we have at least one paragraph
  if (paragraphs.length === 0) {
    paragraphs.push(new Paragraph({
      children: [new TextRun({ text: '' })]
    }));
  }

  const document = new Document({
    sections: [{ children: paragraphs }],
  });

  return Packer.toBlob(document);
}
