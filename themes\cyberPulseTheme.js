
export const cyberPulseTheme = {
  name: 'Cyber Pulse',
  properties: {
    '--bg-primary': '#12121e', // Very dark blue/purple
    '--bg-secondary': '#1a1a2e', // Darker panel elements
    '--bg-app-container': '#1a1a2e',
    '--bg-panel': '#202030', // Slightly lighter for panels
    '--bg-header': '#0d0d1a', // Even darker for header
    '--bg-status-bar': '#0d0d1a',
    '--bg-modal-backdrop': 'rgba(0, 0, 0, 0.7)',
    '--bg-modal-content': '#2a2a3e',
    '--bg-input': '#2a2a3e',
    '--bg-input-disabled': '#3a3a4e',
    '--bg-list-box': '#202030',
    '--bg-list-item-hover': '#303045',
    '--bg-list-item-selected': '#ff00ff', // Magenta accent
    '--bg-ssml-editor-view': '#12121e',
    '--bg-ssml-instructions': '#2a2a3e',
    '--bg-ssml-preview-modal-content': '#1a1a2e',

    '--text-primary': '#e0e0e0', // Light gray
    '--text-secondary': '#b0b0b0', // Softer gray
    '--text-panel-heading': '#00ffff', // Cyan accent
    '--text-header': '#e0e0e0',
    '--text-status-bar': '#e0e0e0',
    '--text-button-primary': '#12121e', // Dark text on bright buttons
    '--text-list-item-selected': '#ffffff',
    '--text-placeholder': '#707080',
    '--text-highlighted': '#12121e', // Dark text on bright highlight
    '--text-search-highlighted': '#12121e',
    '--text-ssml-editor-file-info': '#b0b0b0',
    '--text-ssml-instructions': '#e0e0e0',
    '--text-ssml-tag': '#00ffff', // Cyan
    '--text-ssml-attr-name': '#ff00ff', // Magenta
    '--text-ssml-attr-value': '#39ff14', // Neon green
    '--text-ssml-comment': '#707080',

    '--border-primary': '#3a3a4e', // Darker borders
    '--border-secondary': '#4a4a5e',
    '--border-input': '#00ffff', // Cyan border for inputs
    '--border-ssml-text-widget': '#00ffff',
    '--border-ssml-instructions': '#ff00ff', // Magenta border
    '--border-ssml-preview-modal-content': '#00ffff',

    '--accent-primary': '#00ffff', // Cyan
    '--accent-secondary': '#ff00ff', // Magenta
    '--accent-secondary-hover': '#e600e6', // Darker Magenta
    '--accent-playback': '#39ff14', // Neon green
    '--accent-playback-hover': '#32e612', // Darker Neon green
    '--accent-highlight': '#00ffff', // Cyan highlight bg
    '--accent-search-highlight': '#ff00ff', // Magenta search highlight bg
    '--accent-volume-slider': '#00ffff',

    '--button-primary-bg': '#00ffff', // Cyan buttons
    '--button-primary-hover-bg': '#30ffff',
    '--button-primary-active-bg': '#00e0e0',
    '--button-disabled-bg': '#404050',
    '--button-file-input-bg': '#ff00ff', // Magenta for file inputs
    '--button-file-input-hover-bg': '#ff30ff',
    
    '--button-accent-bg': '#39ff14', // Neon green accent buttons
    '--button-accent-hover-bg': '#60ff40',
    '--button-warning-bg': '#ffff00', // Neon yellow for warning
    '--button-warning-hover-bg': '#ffff30',
    '--button-info-bg': '#00ffff', // Cyan for info
    '--button-info-hover-bg': '#30ffff',
    
    '--shadow-light': 'rgba(0, 255, 255, 0.1)',
    '--shadow-medium': 'rgba(0, 255, 255, 0.2)',
    '--shadow-modal': 'rgba(0, 0, 0, 0.3)',
    '--shadow-inset-active-nav': 'rgba(0,0,0,0.3)',

    '--icon-primary': '#00ffff', // Cyan icons
    '--icon-ms-tts': '#ff00ff', // Magenta for MS TTS icon
    '--icon-selected-item': '#ffffff',

    '--status-processing-color': '#00ffff',
    '--status-ok-color': '#39ff14',
    '--status-flagged-color': '#ffff00',
    '--status-error-color': '#ff3030', // Neon red
    '--status-corrected-color': '#ff00ff',
    '--status-no-text-color': '#707080',
  }
};