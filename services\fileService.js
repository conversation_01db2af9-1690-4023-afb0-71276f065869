// fileService.js - VERIFIED AND CORRECT

import { SUPPORTED_AUDIO_EXTENSIONS, SUPPORTED_DOC_EXTENSIONS } from '../constants.js';
import * as state from '../state.js';
import * as ui from '../ui.js';
import { naturalSort } from '../utils.js';

/**
 * Detect the actual content type of a file by examining its content
 * @param {File} file - The file to analyze
 * @returns {Promise<string>} - 'txt' or 'docx' based on content analysis
 */
export async function detectFileContentType(file) {
    try {
        // If it doesn't have a .docx extension, treat as text
        if (!file.name.toLowerCase().endsWith('.docx')) {
            return 'txt';
        }

        // Check file size (DOCX files should be at least a few hundred bytes)
        if (file.size < 100) {
            console.warn('File is suspiciously small for DOCX:', file.size, 'bytes');
            return 'txt';
        }

        // Try to read the first few bytes to check for ZIP signature
        // DOCX files are ZIP archives, so they should start with 'PK'
        const arrayBuffer = await file.slice(0, 4).arrayBuffer();
        const bytes = new Uint8Array(arrayBuffer);

        // Check for ZIP file signature (PK\x03\x04 or PK\x05\x06 or PK\x07\x08)
        if (bytes[0] === 0x50 && bytes[1] === 0x4B) {
            console.log('File has valid ZIP signature, treating as DOCX');
            return 'docx';
        } else {
            console.log('File does not have valid ZIP signature, treating as text');
            return 'txt';
        }
    } catch (error) {
        console.error('Error detecting file content type:', error);
        // Default to text if detection fails
        return 'txt';
    }
}

export async function handleFolderSelection(event, type) {
    const files = event.target.files;
    if (!files || files.length === 0) return;
    ui.updateStatus(`Loading ${files.length} ${type} file(s)...`);

    const supportedExtensions = type === 'music'
        ? SUPPORTED_AUDIO_EXTENSIONS
        : SUPPORTED_DOC_EXTENSIONS;

    const newFilesArray = Array.from(files).filter(file => {
        const extension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();
        return supportedExtensions.includes(extension);
    });

    if (newFilesArray.length === 0) {
        ui.updateStatus(`No supported files found.`);
        return;
    }

    if (type === 'music') {
        const loadedAudioFilesPromises = newFilesArray.map(file =>
            new Promise((resolve, reject) => {
                const tempAudio = document.createElement('audio');
                const objectURL = URL.createObjectURL(file);
                
                tempAudio.onloadedmetadata = () => {
                    resolve({ 
                        id: file.name + Date.now() + Math.random(), 
                        file, 
                        name: file.name, 
                        duration: tempAudio.duration, 
                        objectURL: URL.createObjectURL(file), // Create a new, persistent URL
                        isSynthesized: false 
                    });
                    URL.revokeObjectURL(objectURL); // Revoke the temporary URL
                };
                tempAudio.onerror = () => {
                    console.warn(`Could not load metadata for ${file.name}. Duration will be 0.`);
                    resolve({ 
                        id: file.name + Date.now() + Math.random(), 
                        file, 
                        name: file.name, 
                        duration: 0, 
                        objectURL: URL.createObjectURL(file), // Keep the URL on error
                        isSynthesized: false 
                    });
                    URL.revokeObjectURL(objectURL); // Revoke the temporary URL
                };
                tempAudio.src = objectURL;
            })
        );
        const musicFilesData = await Promise.all(loadedAudioFilesPromises);
        musicFilesData.sort((a, b) => naturalSort(a.name, b.name));
        state.setMusicFiles(musicFilesData);
        ui.renderMusicPlaylist();
        ui.updateStatus(`Loaded ${state.musicFiles.length} music file(s).`);
    } else { // docs
        // Enhanced file type detection - we'll determine the actual type during loading
        const docFilesData = newFilesArray.map(file => ({
            id: file.name + file.lastModified + Math.random(),
            file,
            name: file.name,
            type: file.name.endsWith('.docx') ? 'docx' : 'txt',
            actualType: null, // Will be determined during document loading
        }));
        docFilesData.sort((a, b) => naturalSort(a.name, b.name));
        state.setDocFiles(docFilesData);
        ui.renderDocList();
        ui.updateStatus(`Loaded ${state.docFiles.length} document file(s).`);
    }
    event.target.value = ''; // Reset file input to allow re-selection of the same folder
    ui.updateAudioControlsUI();
}