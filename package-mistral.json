{"name": "mistral-auto-server", "version": "1.0.0", "description": "Auto-start server for Mistral AI integration", "main": "mistral-auto-server.js", "scripts": {"start": "node mistral-auto-server.js", "dev": "nodemon mistral-auto-server.js", "install-deps": "npm install express cors", "setup": "npm run install-deps && echo 'Setup complete! Run: npm start'"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["mistral", "ai", "vision", "pdf", "text-extraction", "auto-start"], "author": "PDF to Text Converter", "license": "MIT", "engines": {"node": ">=14.0.0"}}