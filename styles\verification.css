
/* Verification Results Panel */
#verification-process-panel {
    width: 100%; /* Take full width if it wraps */
    max-height: 40vh; /* Limit height and allow internal scrolling */
    order: 4; /* Attempt to place it after the three main panels if flex wrapping occurs */
    /* margin-top: 15px; is applied inline in HTML */
}

#verification-results-list li { /* Overrides generic .list-box li for verification specifically */
    display: flex;
    flex-direction: column; /* Stack content vertically */
    align-items: flex-start; /* Align items to the start */
    gap: 8px; /* Space between elements within the list item */
    padding: 10px;
    color: var(--text-primary); 
}

.verification-result-header {
    font-weight: bold;
    color: var(--text-panel-heading);
    width: 100%;
}

.verification-result-details {
    font-size: 0.85em;
    color: var(--text-secondary);
    margin-left: 10px;
    width: 100%;
}
.verification-result-details p {
    margin: 5px 0;
}

.verification-result-actions button {
    width: auto; /* Override full width for these buttons */
    padding: 5px 10px;
    font-size: 0.8em;
    margin-right: 5px;
}
.verification-result-actions button .material-icons {
    font-size: 1em; /* Smaller icon for action buttons */
}

.status-processing, .status-correcting-with-google-tts, .status-correcting-with-microsoft-tts { 
    color: var(--status-processing-color); 
} 
.status-checked-ok, .status-ok, .status-reprocessed-with-correction, .status-replaced-with-correction, .status-corrected-reprocessed, .status-corrected-amp-reprocessed { 
    color: var(--status-ok-color); 
}
.status-flagged-needs-reprocessing { 
    color: var(--status-flagged-color); 
    font-weight: bold; 
}
.status-error-during-verification, .status-error-in-google-tts, .status-error-in-microsoft-tts, .status-error-no-tts-provider-configured-selected-for-reprocessing, .status-error { 
    color: var(--status-error-color); 
    font-weight: bold; 
}
/* .status-corrected { color: var(--status-corrected-color); font-weight: bold; }  Original general var, use specifics now */
.status-no-text-match { 
    color: var(--status-no-text-color); 
}

#verification-overall-status {
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-secondary);
    color: var(--text-primary);
}