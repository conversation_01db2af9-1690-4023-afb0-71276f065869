# Web Media Player and Document Editor

A comprehensive web application for audio transcription, text-to-speech synthesis, and document editing with AI-powered pronunciation and pacing analysis.

## Features

- **Audio Transcription**: Local Whisper AI transcription with pronunciation analysis
- **Text-to-Speech**: Google Cloud TTS and Microsoft Azure TTS integration
- **Document Editing**: Support for TXT and DOCX files
- **AI Analysis**: Pronunciation and pacing issue detection with recommendations
- **Theme Support**: Multiple UI themes available

## Run Locally

**Prerequisites:**
- Node.js (v16 or higher)
- Python 3.8+ (for Whisper transcription)

### Quick Start

1. **Install Node.js dependencies:**
   ```bash
   npm install
   ```

2. **Install Python dependencies for Whisper (optional but recommended):**
   ```bash
   pip install openai-whisper flask flask-cors
   ```

3. **Set up API keys (optional):**
   - Create `.env.local` file
   - Add your API keys:
     ```
     GEMINI_API_KEY=your_gemini_key_here
     G_TTS_API_KEY=your_google_tts_key_here
     MS_TTS_API_KEY=your_azure_tts_key_here
     MS_TTS_SERVICE_REGION=your_azure_region_here
     ```

4. **Run the application:**
   ```bash
   npm run dev
   ```

   This automatically starts:
   - Web application on `http://localhost:5173`
   - Whisper transcription server on `http://localhost:8001`

### Alternative Commands

- **Web app only:** `npm run dev:vite-only`
- **Whisper server only:** `npm run whisper`
- **Manual concurrent start:** `npm run dev:full`

## Whisper Transcription Features

The application includes advanced pronunciation and pacing analysis:

- **Mispronunciation Detection**: Identifies words with low confidence scores
- **Pacing Analysis**: Detects speaking too fast, too slow, or unusual pauses
- **Word-Level Timing**: Precise timestamps for each detected issue
- **Recommendations**: Specific advice for pronunciation and pacing improvements
- **Scoring System**: 0-100 overall score with color-coded feedback

### Using Whisper Analysis

1. Load audio files into the application
2. Click "AI Whisper Audio Check" button
3. Select single file or batch processing
4. Choose Whisper model (base recommended for balance of speed/quality)
5. View detailed analysis with specific recommendations

## Troubleshooting

### Whisper Server Issues

If you see "Whisper server is not running":

1. **Check Python installation:** `python --version`
2. **Install dependencies:** `pip install openai-whisper flask flask-cors`
3. **Manual server start:** `python whisper_server.py`
4. **Check port availability:** Ensure port 8001 is not in use

### Common Issues

- **Port conflicts**: Change ports in `constants.js` if needed
- **API key errors**: Verify API keys are correctly set in environment variables
- **File upload issues**: Check file formats are supported (MP3, WAV, TXT, DOCX)
