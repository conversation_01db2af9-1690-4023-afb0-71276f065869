/**
 * docxEditor.js - Utility for editing DOCX files
 * Provides functions to convert DOCX to editable text and back to DOCX
 */

import { convertHtmlToDocxBlob } from './docxGenerator.js';

/**
 * Extract plain text from a DOCX file for editing
 * @param {File} file - The DOCX file
 * @returns {Promise<string>} - Plain text content
 */
export async function extractTextFromDocx(file) {
    try {
        const arrayBuffer = await file.arrayBuffer();
        
        if (window.mammoth) {
            const result = await window.mammoth.extractRawText({ arrayBuffer });
            return result.value;
        } else {
            throw new Error('Mammoth.js library not loaded');
        }
    } catch (error) {
        console.error('Error extracting text from DOCX:', error);
        throw error;
    }
}

/**
 * Create a new DOCX file from plain text
 * @param {string} text - The plain text content
 * @param {string} filename - Original filename for reference
 * @returns {Promise<Blob>} - DOCX file as blob
 */
export async function createDocxFromText(text, filename) {
    try {
        console.log('Creating DOCX from text, length:', text.length);

        // Check if the content is SSML
        const isSSMLContent = text.trim().startsWith('<speak>') && text.trim().endsWith('</speak>');
        console.log('Content is SSML:', isSSMLContent);

        let htmlContent;

        if (isSSMLContent) {
            // For SSML content, preserve it as a single block with monospace formatting
            htmlContent = `<div style="font-family: 'Courier New', monospace; white-space: pre-wrap; background-color: #f8f8f8; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">${escapeHtml(text)}</div>`;
        } else {
            // Convert plain text to HTML with proper paragraph formatting
            // Handle empty lines and preserve formatting
            const lines = text.split('\n');
            htmlContent = lines
                .map(line => {
                    const trimmedLine = line.trim();
                    if (trimmedLine === '') {
                        return '<p>&nbsp;</p>';
                    } else {
                        return `<p>${escapeHtml(line)}</p>`;
                    }
                })
                .join('');
        }

        console.log('Converting HTML to DOCX using new generator...');

        // Convert HTML to DOCX using the new generator
        const docxBlob = await convertHtmlToDocxBlob(htmlContent);

        console.log('DOCX creation successful, blob size:', docxBlob.size);
        return docxBlob;
    } catch (error) {
        console.error('Error creating DOCX from text:', error);
        console.error('Error details:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });
        throw error;
    }
}

/**
 * Escape HTML special characters
 * @param {string} text - Text to escape
 * @returns {string} - Escaped text
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Check if DOCX editing libraries are available
 * @returns {boolean} - True if libraries are loaded
 */
export function isDocxEditingAvailable() {
    return !!window.mammoth;
}

/**
 * Get a user-friendly error message for missing libraries
 * @returns {string} - Error message
 */
export function getDocxEditingErrorMessage() {
    if (!window.mammoth) {
        return 'Mammoth.js library not loaded. Cannot read DOCX files.';
    }
    return 'DOCX editing libraries not properly initialized.';
}

/**
 * Validate if a file appears to be a valid DOCX file
 * @param {File} file - The file to validate
 * @returns {Promise<boolean>} - True if file appears to be valid DOCX
 */
export async function validateDocxFile(file) {
    try {
        // Check file extension
        if (!file.name.toLowerCase().endsWith('.docx')) {
            return false;
        }

        // Check file size (should be at least a few hundred bytes for a valid DOCX)
        if (file.size < 100) {
            console.warn('DOCX file is suspiciously small:', file.size, 'bytes');
            return false;
        }

        // Try to read the first few bytes to check for ZIP signature
        // DOCX files are ZIP archives, so they should start with 'PK'
        const arrayBuffer = await file.slice(0, 4).arrayBuffer();
        const bytes = new Uint8Array(arrayBuffer);

        // Check for ZIP file signature (PK\x03\x04 or PK\x05\x06 or PK\x07\x08)
        if (bytes[0] === 0x50 && bytes[1] === 0x4B) {
            console.log('File has valid ZIP signature');
            return true;
        } else {
            console.warn('File does not have valid ZIP signature:', bytes);
            return false;
        }
    } catch (error) {
        console.error('Error validating DOCX file:', error);
        return false;
    }
}
