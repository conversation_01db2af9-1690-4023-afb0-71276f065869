#!/bin/bash

echo "🚀 Starting Mistral Auto-Start Server..."
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if package.json exists
if [ ! -f "package-mistral.json" ]; then
    echo "❌ package-mistral.json not found"
    echo "Please ensure you're in the correct directory"
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install express cors
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
fi

echo "✅ Starting Mistral Auto-Start Server on port 3001..."
echo
echo "📋 The server will provide these features:"
echo "   - Auto-start Mistral Docker containers"
echo "   - Fallback mock server for testing"
echo "   - API endpoints for server management"
echo
echo "🌐 Server will be available at: http://localhost:3001"
echo "📖 API Documentation: http://localhost:3001/health"
echo
echo "Press Ctrl+C to stop the server"
echo

# Start the server
node mistral-auto-server.js
