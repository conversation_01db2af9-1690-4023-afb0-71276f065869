
/* SSML Editor Styles */
#text-editor-view-container.ssml-editor-view {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding: 15px;
    background-color: var(--bg-ssml-editor-view); 
    box-sizing: border-box;
    overflow-y: hidden; 
    gap: 10px;
}

#ssml-editor-heading {
    font-size: 1.5em;
    color: var(--text-panel-heading);
    margin-bottom: 5px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-primary);
}
#ssml-editor-file-info {
    font-size: 0.9em;
    color: var(--text-ssml-editor-file-info);
    margin-bottom: 10px;
}

.ssml-editor-layout {
    display: flex;
    gap: 15px;
    flex-grow: 1;
    overflow: hidden; 
    min-height: 0; 
}

.ssml-controls-panel {
    flex: 0 0 280px; 
    background-color: var(--bg-panel);
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 1px 3px var(--shadow-light);
    display: flex;
    flex-direction: column;
    gap: 15px;
    overflow-y: auto;
}

/* Style for the SSML file input label */
#ssml-file-input-label {
    background-color: var(--button-file-input-bg); 
    color: var(--text-button-primary);
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    font-size: 0.9em;
    display: block; 
    margin-bottom: 10px; 
    transition: background-color 0.2s;
}
#ssml-file-input-label:hover {
    background-color: var(--button-file-input-hover-bg);
}

.ssml-control-group {
    display: flex;
    flex-direction: column;
    gap: 8px; 
    padding-bottom: 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid var(--border-secondary);
}
.ssml-control-group:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
}

.ssml-control-group h3 {
    font-size: 1em;
    color: var(--text-panel-heading); 
    margin-bottom: 8px;
    padding-bottom: 4px;
    border-bottom: 1px solid var(--border-primary);
}


.ssml-editor-main {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    background-color: var(--bg-panel);
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 1px 3px var(--shadow-light);
    min-width: 0; 
    overflow: hidden; 
}

.ssml-instructions-area {
    font-size: 0.85em;
    color: var(--text-ssml-instructions);
    background-color: var(--bg-ssml-instructions);
    padding: 10px;
    border-radius: 4px;
    border: 1px solid var(--border-ssml-instructions);
    max-height: 150px; 
    overflow-y: auto;
}
.ssml-instructions-area ul {
    margin: 5px 0 0 15px;
    padding-left: 10px;
}
.ssml-instructions-area li {
    margin-bottom: 3px;
}


#ssml-text-widget {
    flex-grow: 1;
    width: 100%;
    border: 1px solid var(--border-ssml-text-widget);
    border-radius: 4px;
    padding: 10px;
    font-family: "Consolas", "Monaco", monospace;
    font-size: 0.95em;
    line-height: 1.5;
    box-sizing: border-box;
    resize: none; 
    min-height: 200px; 
    overflow-y: auto;
    background-color: var(--bg-input);
    color: var(--text-primary);
}

.ssml-search-replace {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 8px 0; 
    flex-shrink: 0; 
}

.ssml-search-replace input[type="text"] {
    padding: 8px;
    border: 1px solid var(--border-input); 
    border-radius: 4px;
    font-size: 0.9em;
    flex-grow: 1;
    background-color: var(--bg-input);
    color: var(--text-primary);
}

.ssml-search-replace button {
    width: auto; 
    flex-shrink: 0;
}

#ssml-status-bar {
    background-color: var(--bg-status-bar); 
    color: var(--text-status-bar);
    padding: 6px 12px;
    font-size: 0.85em;
    text-align: left;
    flex-shrink: 0;
    margin-top: auto; 
    border-radius: 0 0 6px 6px; 
}

/* SSML Modals */
.ssml-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-modal-backdrop);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000; 
}

.ssml-modal-content {
    background-color: var(--bg-modal-content);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 15px var(--shadow-modal);
    width: 90%;
    max-width: 450px;
}
.ssml-modal-content.large {
    max-width: 700px;
}


.ssml-modal-content h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--text-panel-heading);
}

.ssml-modal-content p {
    margin-bottom: 10px;
    font-size: 0.95em;
    color: var(--text-primary);
}

.ssml-modal-content input[type="number"] {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border: 1px solid var(--border-input); 
    border-radius: 4px;
    box-sizing: border-box;
    background-color: var(--bg-input);
    color: var(--text-primary);
}

.ssml-modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.ssml-modal-buttons button {
    width: auto; 
}

/* SSML Preview Syntax Highlighting (Basic) */
#ssml-preview-content pre .ssml-tag,
#view-ssml-modal-content-area pre .ssml-tag {
    color: var(--text-ssml-tag); 
    font-weight: bold;
}
#ssml-preview-content pre .ssml-attr-name,
#view-ssml-modal-content-area pre .ssml-attr-name {
    color: var(--text-ssml-attr-name); 
}
#ssml-preview-content pre .ssml-attr-value,
#view-ssml-modal-content-area pre .ssml-attr-value {
    color: var(--text-ssml-attr-value); 
}
#ssml-preview-content pre .ssml-comment,
#view-ssml-modal-content-area pre .ssml-comment {
    color: var(--text-ssml-comment); 
    font-style: italic;
}

#ssml-preview-content { 
    background: var(--bg-ssml-preview-modal-content);
    border: 1px solid var(--border-ssml-preview-modal-content);
}

#view-ssml-modal-content-area { 
    background: var(--bg-input); 
    border: 1px solid var(--border-primary);
}