/* Whisper Audio Check Modal Styles */

.whisper-modal-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    max-height: 80vh;
    overflow-y: auto;
}

.whisper-control-group {
    border: 1px solid var(--border-secondary);
    border-radius: 8px;
    padding: 15px;
    background-color: var(--bg-panel);
}

.whisper-control-group h4 {
    margin: 0 0 10px 0;
    color: var(--text-panel-heading);
    font-size: 1.1em;
}

.whisper-control-group .radio-group {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.whisper-control-group .radio-group input[type="radio"] {
    margin-right: 5px;
}

.whisper-control-group .radio-group label {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--text-primary);
}

.whisper-control-group label {
    display: block;
    margin-bottom: 5px;
    color: var(--text-primary);
    font-weight: 500;
}

.whisper-control-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    background-color: var(--bg-input);
    color: var(--text-primary);
    font-size: 14px;
}

.whisper-control-group select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px var(--accent-primary-alpha);
}

#whisper-single-file-controls {
    margin-top: 10px;
}

.whisper-output {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
    background-color: var(--bg-input);
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
}

.whisper-log {
    margin-bottom: 10px;
    padding: 8px;
    border-radius: 4px;
    border-left: 4px solid var(--border-secondary);
}

.whisper-log.info {
    background-color: var(--bg-info, #e3f2fd);
    border-left-color: var(--status-processing-color, #2196f3);
    color: var(--text-info, #0d47a1);
}

.whisper-log.success {
    background-color: var(--bg-success, #e8f5e8);
    border-left-color: var(--status-ok-color, #4caf50);
    color: var(--text-success, #2e7d32);
}

.whisper-log.warning {
    background-color: var(--bg-warning, #fff3e0);
    border-left-color: var(--status-flagged-color, #ff9800);
    color: var(--text-warning, #ef6c00);
}

.whisper-log.error {
    background-color: var(--bg-error, #ffebee);
    border-left-color: var(--status-error-color, #f44336);
    color: var(--text-error, #c62828);
}

.whisper-log small {
    opacity: 0.7;
    font-size: 11px;
}

.whisper-result {
    margin: 15px 0;
    padding: 15px;
    border: 1px solid var(--border-secondary);
    border-radius: 6px;
    background-color: var(--bg-secondary);
}

.whisper-result.error {
    border-color: var(--status-error-color);
    background-color: var(--bg-error, #ffebee);
}

.whisper-result h5 {
    margin: 0 0 8px 0;
    color: var(--text-panel-heading);
    font-size: 1em;
    font-weight: 600;
}

.whisper-result h6 {
    margin: 0 0 10px 0;
    color: var(--text-secondary);
    font-size: 0.9em;
    font-weight: 500;
}

.whisper-result .transcript-content {
    margin-top: 10px;
}

.whisper-result .transcript-content strong {
    color: var(--text-primary);
    font-weight: 600;
}

.whisper-result .transcript-content p {
    margin: 5px 0 0 0;
    padding: 10px;
    background-color: var(--bg-input);
    border: 1px solid var(--border-primary);
    border-radius: 4px;
    color: var(--text-primary);
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.whisper-result .transcript-content p.error {
    background-color: var(--bg-error, #ffebee);
    border-color: var(--status-error-color);
    color: var(--text-error, #c62828);
}

.whisper-summary {
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--bg-success, #e8f5e8);
    border: 1px solid var(--status-ok-color);
    border-radius: 6px;
}

.whisper-summary h4 {
    margin: 0 0 10px 0;
    color: var(--text-success, #2e7d32);
    font-size: 1.1em;
}

.whisper-summary p {
    margin: 5px 0;
    color: var(--text-success, #2e7d32);
    font-size: 0.9em;
}

.whisper-modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 15px;
    border-top: 1px solid var(--border-secondary);
}

.whisper-modal-actions button {
    min-width: 120px;
}

/* Pronunciation and Pacing Analysis Styles */

.analysis-section {
    margin-top: 20px;
    padding: 15px;
    background-color: var(--bg-panel);
    border: 1px solid var(--border-secondary);
    border-radius: 6px;
}

.analysis-section h6 {
    margin: 0 0 15px 0;
    color: var(--text-panel-heading);
    font-size: 1.1em;
    font-weight: 600;
    border-bottom: 1px solid var(--border-secondary);
    padding-bottom: 8px;
}

.analysis-section h7 {
    display: block;
    margin: 15px 0 8px 0;
    color: var(--text-primary);
    font-size: 1em;
    font-weight: 600;
}

.analysis-score {
    margin-bottom: 15px;
    padding: 10px;
    background-color: var(--bg-input);
    border-radius: 4px;
    text-align: center;
}

.analysis-score strong {
    font-size: 1.1em;
    margin-right: 10px;
}

.score-excellent {
    color: #2e7d32;
    font-weight: 600;
}

.score-good {
    color: #388e3c;
    font-weight: 600;
}

.score-fair {
    color: #f57c00;
    font-weight: 600;
}

.score-poor {
    color: #d32f2f;
    font-weight: 600;
}

.pronunciation-issues,
.pacing-issues,
.general-recommendations {
    margin-bottom: 15px;
}

.pronunciation-issues ul,
.pacing-issues ul,
.general-recommendations ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
    list-style-type: none;
}

.pronunciation-issues li,
.pacing-issues li,
.general-recommendations li {
    margin-bottom: 8px;
    padding: 8px 12px;
    border-radius: 4px;
    border-left: 4px solid;
    background-color: var(--bg-input);
    line-height: 1.4;
}

.issue-high,
.rec-high {
    border-left-color: var(--status-error-color, #f44336);
    background-color: var(--bg-error, #ffebee);
    color: var(--text-error, #c62828);
}

.issue-medium,
.rec-medium {
    border-left-color: var(--status-flagged-color, #ff9800);
    background-color: var(--bg-warning, #fff3e0);
    color: var(--text-warning, #ef6c00);
}

.issue-low,
.rec-low {
    border-left-color: var(--status-processing-color, #2196f3);
    background-color: var(--bg-info, #e3f2fd);
    color: var(--text-info, #0d47a1);
}

.no-analysis {
    color: var(--text-secondary);
    font-style: italic;
    text-align: center;
    padding: 20px;
    margin: 0;
}

/* Enhanced whisper result styling */
.whisper-result .transcript-content {
    margin-bottom: 10px;
}

.whisper-result .transcript-content p {
    margin-bottom: 15px;
}

/* Server error styling */
.server-error {
    background-color: var(--bg-error, #ffebee);
    border: 1px solid var(--status-error-color, #f44336);
    border-radius: 6px;
    padding: 20px;
    margin: 15px 0;
}

.server-error h4 {
    margin: 0 0 15px 0;
    color: var(--text-error, #c62828);
    font-size: 1.2em;
}

.server-error p {
    margin: 10px 0;
    color: var(--text-error, #c62828);
    line-height: 1.5;
}

.server-error ol {
    margin: 10px 0;
    padding-left: 20px;
    color: var(--text-error, #c62828);
}

.server-error li {
    margin: 8px 0;
    line-height: 1.4;
}

.server-error code {
    background-color: var(--bg-input);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .whisper-modal-content {
        max-height: 90vh;
    }
    
    .whisper-control-group .radio-group {
        flex-direction: column;
        gap: 10px;
    }
    
    .whisper-modal-actions {
        flex-direction: column;
    }
    
    .whisper-modal-actions button {
        width: 100%;
    }
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
    .whisper-log.info {
        background-color: rgba(33, 150, 243, 0.1);
        color: #90caf9;
    }
    
    .whisper-log.success {
        background-color: rgba(76, 175, 80, 0.1);
        color: #a5d6a7;
    }
    
    .whisper-log.warning {
        background-color: rgba(255, 152, 0, 0.1);
        color: #ffcc02;
    }
    
    .whisper-log.error {
        background-color: rgba(244, 67, 54, 0.1);
        color: #ef5350;
    }
    
    .whisper-summary {
        background-color: rgba(76, 175, 80, 0.1);
    }
    
    .whisper-result.error {
        background-color: rgba(244, 67, 54, 0.1);
    }
}
