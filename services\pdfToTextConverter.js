// pdfToTextConverter.js - PDF to Text Converter Module

import * as dom from '../domElements.js';

// --- Constants ---
const AZURE_OCR_MODEL_ID = "prebuilt-read";
const API_RETRY_WAIT_SECONDS = 30;
const MAX_RETRIES = 3;
const IMAGE_FORMAT = "PNG";

// --- Module State ---
let currentPdfFile = null;
let pdfDocument = null;
let azureClient = null;
let conversionResults = [];
let isProcessing = false;
let mistralServerProcess = null;
let isAutoStarting = false;
let azureApiPath = '/documentintelligence'; // Will be determined during connection test

// --- Configuration ---
let config = {
    ocrEngine: 'ollama', // 'azure', 'mistral', or 'ollama'
    azureEndpoint: '',
    azureKey: '',
    mistralUrl: 'http://localhost:8000',
    mistralApiKey: '',
    mistralModel: 'mistral-large-latest',
    ollamaUrl: 'http://localhost:11434',
    ollamaModel: 'llava:7b',
    imageDpi: 300,
    saveImages: true,
    batchSize: 2,  // Process 2 pages simultaneously for speed (Moondream can handle this)
    fastMode: true, // Enable speed optimizations
    parallelProcessing: true // Enable parallel processing for Ollama
};

// --- Utility Functions ---

/**
 * Updates the status bar with a message and type
 */
function updateStatus(message, type = "info") {
    if (dom.pdfToTextStatusBar) {
        dom.pdfToTextStatusBar.textContent = message;
        dom.pdfToTextStatusBar.className = `status-bar ${type}`;
    }
}

/**
 * Adds a log entry to the conversion log
 */
function addLogEntry(message, type = "info") {
    if (!dom.conversionLog) return;
    
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.className = `log-entry ${type}`;
    logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
    
    dom.conversionLog.appendChild(logEntry);
    dom.conversionLog.scrollTop = dom.conversionLog.scrollHeight;
}

/**
 * Updates the progress bar
 */
function updateProgress(percentage, text) {
    if (dom.pdfProgressBar) {
        dom.pdfProgressBar.style.setProperty('--progress-width', `${percentage}%`);
        dom.pdfProgressBar.classList.add('active');
    }
    if (dom.pdfProgressText) {
        dom.pdfProgressText.textContent = text;
    }
}

/**
 * Cleans extracted text by removing common artifacts and image descriptions
 */
function cleanExtractedText(text) {
    if (!text) return "";

    text = text.trim();

    // Remove common image description patterns that vision models add
    const imageDescriptionPatterns = [
        /^The image shows?.*$/gm,
        /^This image shows?.*$/gm,
        /^In this image.*$/gm,
        /^The picture shows?.*$/gm,
        /^This picture shows?.*$/gm,
        /^The photo shows?.*$/gm,
        /^This photo shows?.*$/gm,
        /^The document shows?.*$/gm,
        /^This document shows?.*$/gm,
        /^The page shows?.*$/gm,
        /^This page shows?.*$/gm,
        /^I can see.*$/gm,
        /^Looking at.*$/gm,
        /^The text appears.*$/gm,
        /^The book appears.*$/gm,
        /^\d+\.\s*The image.*$/gm,
        /^\d+\.\s*This image.*$/gm,
        /^The image contains.*$/gm,
        /^This appears to be.*$/gm,
        /^It appears.*$/gm,
        /^There is.*book.*$/gm,
        /^There are.*words.*$/gm,
        /^The cover.*$/gm,
        /^The title.*$/gm
    ];

    // Remove image description patterns
    for (const pattern of imageDescriptionPatterns) {
        text = text.replace(pattern, '');
    }

    // Remove numbered list markers that are just descriptions
    text = text.replace(/^\d+\.\s*$/gm, '');

    // Clean up formatting
    text = text.replace(/\f/g, '\n'); // Replace form feeds with newlines
    text = text.replace(/ +/g, ' '); // Replace multiple spaces with single space
    text = text.replace(/\n\s*\n\s*\n/g, '\n\n'); // Remove excessive line breaks
    text = text.replace(/^\s*\n/gm, ''); // Remove empty lines at start

    return text.trim();
}

/**
 * Formats text into paragraphs
 */
function formatTextToParagraphs(text) {
    if (!text) return [];
    text = text.replace(/\r\n/g, "\n").replace(/\r/g, "\n");
    const paragraphs = text.split(/\n\s*\n+/);
    return paragraphs.map(p => p.trim()).filter(p => p.length > 0);
}

// --- Azure AI Document Intelligence Functions ---

/**
 * Tests the Azure connection
 */
async function testAzureConnection() {
    if (!config.azureEndpoint || !config.azureKey) {
        addLogEntry("Azure endpoint and key are required", "error");
        return false;
    }

    // Validate endpoint format
    if (!config.azureEndpoint.startsWith('https://') || !config.azureEndpoint.includes('.cognitiveservices.azure.com')) {
        addLogEntry("Azure endpoint should be in format: https://yourservice.cognitiveservices.azure.com", "error");
        return false;
    }

    try {
        addLogEntry("Testing Azure connection...", "info");
        addLogEntry(`Endpoint: ${config.azureEndpoint}`, "info");

        // Create a simple test request - try the newer API format first
        let response = await fetch(`${config.azureEndpoint}/formrecognizer/documentModels?api-version=2023-07-31`, {
            method: 'GET',
            headers: {
                'Ocp-Apim-Subscription-Key': config.azureKey,
                'Content-Type': 'application/json'
            }
        });

        let apiPathUsed = '/formrecognizer';

        // If that fails, try the older documentintelligence format
        if (!response.ok && response.status === 404) {
            addLogEntry("Trying alternative API endpoint format...", "info");
            response = await fetch(`${config.azureEndpoint}/documentintelligence/documentModels?api-version=2023-07-31`, {
                method: 'GET',
                headers: {
                    'Ocp-Apim-Subscription-Key': config.azureKey,
                    'Content-Type': 'application/json'
                }
            });
            apiPathUsed = '/documentintelligence';
        }

        if (response.ok) {
            azureApiPath = apiPathUsed; // Store the working API path
            addLogEntry(`Azure connection successful using ${apiPathUsed} API`, "success");
            return true;
        } else {
            addLogEntry(`Azure connection failed: ${response.status} ${response.statusText}`, "error");
            if (response.status === 404) {
                addLogEntry("404 error suggests the endpoint URL may be incorrect", "error");
                addLogEntry("Verify your Azure endpoint format: https://yourservice.cognitiveservices.azure.com", "info");
            }
            return false;
        }
    } catch (error) {
        addLogEntry(`Azure connection error: ${error.message}`, "error");
        return false;
    }
}

/**
 * Extracts text from an image using Azure AI Document Intelligence
 */
async function extractTextWithAzureDI(imageBlob, pageNumber) {
    addLogEntry(`Analyzing page ${pageNumber} with Azure DI...`, "info");

    let retries = 0;
    let lastError = null;

    while (retries < MAX_RETRIES) {
        try {
            // Start the analysis using the detected API path
            const analyzeResponse = await fetch(`${config.azureEndpoint}${azureApiPath}/documentModels/${AZURE_OCR_MODEL_ID}:analyze?api-version=2023-07-31`, {
                method: 'POST',
                headers: {
                    'Ocp-Apim-Subscription-Key': config.azureKey,
                    'Content-Type': 'application/octet-stream'
                },
                body: imageBlob
            });

            if (!analyzeResponse.ok) {
                throw new Error(`HTTP ${analyzeResponse.status}: ${analyzeResponse.statusText}`);
            }

            const operationLocation = analyzeResponse.headers.get('Operation-Location');
            if (!operationLocation) {
                throw new Error('No operation location returned from Azure');
            }

            // Poll for results
            let result = null;
            let pollAttempts = 0;
            const maxPollAttempts = 60; // 5 minutes max

            while (pollAttempts < maxPollAttempts) {
                await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
                
                const resultResponse = await fetch(operationLocation, {
                    headers: {
                        'Ocp-Apim-Subscription-Key': config.azureKey
                    }
                });

                if (!resultResponse.ok) {
                    throw new Error(`Polling failed: ${resultResponse.status}`);
                }

                const resultData = await resultResponse.json();
                
                if (resultData.status === 'succeeded') {
                    result = resultData.analyzeResult;
                    break;
                } else if (resultData.status === 'failed') {
                    throw new Error('Azure analysis failed');
                } else if (resultData.status === 'running') {
                    addLogEntry(`Page ${pageNumber}: Analysis in progress...`, "info");
                    pollAttempts++;
                } else {
                    throw new Error(`Unexpected status: ${resultData.status}`);
                }
            }

            if (!result) {
                throw new Error('Analysis timed out');
            }

            if (result.content) {
                addLogEntry(`Page ${pageNumber}: Extracted ${result.content.length} characters`, "success");
                return { content: result.content, error: null };
            } else {
                addLogEntry(`Page ${pageNumber}: No text content found`, "warning");
                return { content: "", error: null };
            }

        } catch (error) {
            lastError = error;
            addLogEntry(`Page ${pageNumber}: Error (attempt ${retries + 1}): ${error.message}`, "warning");
            
            if (error.message.includes('429') || error.message.includes('throttle')) {
                retries++;
                if (retries < MAX_RETRIES) {
                    addLogEntry(`Page ${pageNumber}: Rate limited, waiting ${API_RETRY_WAIT_SECONDS}s...`, "info");
                    await new Promise(resolve => setTimeout(resolve, API_RETRY_WAIT_SECONDS * 1000));
                    continue;
                }
            } else if (error.message.includes('401') || error.message.includes('403')) {
                return { content: null, error: "Authentication failed. Check Azure credentials." };
            } else {
                retries++;
                if (retries < MAX_RETRIES) {
                    addLogEntry(`Page ${pageNumber}: Retrying in ${API_RETRY_WAIT_SECONDS}s...`, "info");
                    await new Promise(resolve => setTimeout(resolve, API_RETRY_WAIT_SECONDS * 1000));
                    continue;
                }
            }
        }
    }

    return { content: null, error: `Failed after ${MAX_RETRIES} retries: ${lastError?.message}` };
}

// --- Mistral Vision Functions ---

/**
 * Tests the Mistral connection
 */
async function testMistralConnection() {
    if (!config.mistralUrl) {
        addLogEntry("Mistral URL is required", "error");
        return false;
    }

    try {
        addLogEntry("Testing Mistral connection...", "info");

        // Test with a simple health check or models endpoint
        const response = await fetch(`${config.mistralUrl}/v1/models`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...(config.mistralApiKey && { 'Authorization': `Bearer ${config.mistralApiKey}` })
            }
        });

        if (response.ok) {
            const data = await response.json();
            addLogEntry(`Mistral connection successful. Available models: ${data.data?.length || 0}`, "success");
            return true;
        } else {
            addLogEntry(`Mistral connection failed: ${response.status} ${response.statusText}`, "error");
            return false;
        }
    } catch (error) {
        addLogEntry(`Mistral connection error: ${error.message}`, "error");
        return false;
    }
}

/**
 * Converts image blob to base64 for Mistral API
 */
async function imageToBase64(imageBlob) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
            const base64 = reader.result.split(',')[1]; // Remove data:image/png;base64, prefix
            resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(imageBlob);
    });
}

/**
 * Extracts text from an image using Ollama Vision API (LLaVA)
 */
async function extractTextWithOllama(imageBlob, pageNumber) {
    const startTime = Date.now();
    addLogEntry(`Analyzing page ${pageNumber} with ${config.ollamaModel}...`, "info");

    let retries = 0;
    let lastError = null;

    while (retries < MAX_RETRIES) {
        try {
            // Convert image to base64
            const conversionStart = Date.now();
            const base64Image = await imageToBase64(imageBlob);
            const conversionTime = Date.now() - conversionStart;
            addLogEntry(`Page ${pageNumber}: Image conversion took ${conversionTime}ms`, "info");

            // Prepare the request payload for Ollama with aggressive speed optimizations
            const isFastMode = config.fastMode !== false; // Default to true
            const isUltraFast = config.ollamaModel?.includes('moondream');

            let prompt, options;

            if (isFastMode && isUltraFast) {
                // Ultra-fast mode for Moondream - very specific OCR prompt
                prompt = `Read all the text in this image. Output only the text content, no descriptions or explanations. If you see words, sentences, or paragraphs, transcribe them exactly as they appear. Do not describe what you see, just extract the text.`;
                options = {
                    temperature: 0,         // Deterministic for speed
                    top_p: 0.8,            // Very focused sampling
                    num_predict: 1000,     // Allow more text output
                    num_ctx: 1024,         // Slightly larger context for text
                    repeat_penalty: 1.0,   // No penalty processing
                    top_k: 20,             // Reasonable vocabulary for text
                    num_thread: 4          // Use multiple threads
                };
            } else if (isFastMode) {
                // Fast mode for other models - clear OCR instructions
                prompt = `This is an OCR task. Read and transcribe all text visible in this image. Output only the actual text content, no image descriptions. If there are words, sentences, or paragraphs, write them out exactly as they appear. Do not describe the image or layout.`;
                options = {
                    temperature: 0.1,
                    top_p: 0.9,
                    num_predict: 1500,
                    num_ctx: 2048,
                    repeat_penalty: 1.1,
                    top_k: 30
                };
            } else {
                // Standard mode - comprehensive OCR prompt
                prompt = `Perform OCR (Optical Character Recognition) on this PDF page image. Extract and transcribe all visible text content exactly as it appears. Include all words, sentences, paragraphs, headings, and any other text elements. Do not describe the image, layout, or visual elements - only output the actual text content. If there is no readable text, respond with "No text found". This is page ${pageNumber} of a PDF document.`;
                options = {
                    temperature: 0.1,
                    num_predict: 3000,
                    num_ctx: 4096
                };
            }

            const payload = {
                model: config.ollamaModel || "llava:7b",
                prompt: prompt,
                images: [base64Image],
                stream: false,
                options: options
            };

            addLogEntry(`Page ${pageNumber}: Sending request to Ollama...`, "info");
            const apiStart = Date.now();

            const response = await fetch('http://localhost:11434/api/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            const apiTime = Date.now() - apiStart;
            addLogEntry(`Page ${pageNumber}: Ollama API call took ${apiTime}ms`, "info");

            if (!response.ok) {
                throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();

            if (result.response) {
                const extractedText = result.response.trim();
                const totalTime = Date.now() - startTime;

                // Check if the response looks like an image description rather than OCR
                const imageDescriptionRegex = /^(The image|This image|The picture|This picture|The photo|This photo|The document|This document|The page|This page|I can see|Looking at|The text appears|The book appears|\d+\.\s*The image|\d+\.\s*This image)/i;
                const isImageDescription = imageDescriptionRegex.test(extractedText);

                if (isImageDescription) {
                    addLogEntry(`Page ${pageNumber}: Model gave image description instead of text. Retrying with stronger OCR prompt...`, "warning");

                    // --- START: CORRECTED RETRY BLOCK ---
                    const retryPayload = {
                        model: config.ollamaModel || "llava:7b",
                        prompt: `IMPORTANT: This is OCR (text recognition). Do NOT describe the image. Only transcribe the actual text you can read. If you see printed words, type them out exactly. Do not say "The image shows" or describe anything. Just output the readable text content.`,
                        images: [base64Image],
                        stream: false,
                        options: {
                            temperature: 0,
                            num_predict: 2000,
                            num_ctx: 2048
                        }
                    };

                    const retryResponse = await fetch('http://localhost:11434/api/generate', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(retryPayload)
                    });

                    if (retryResponse.ok) {
                        const retryResult = await retryResponse.json();
                        if (retryResult.response) {
                            const retryText = retryResult.response.trim();
                            // FIX 1: Re-evaluate the description pattern on the NEW text
                            const isRetryStillDescription = imageDescriptionRegex.test(retryText);

                            if (retryText && !isRetryStillDescription) {
                                addLogEntry(`Page ${pageNumber}: Retry successful - extracted ${retryText.length} characters in ${totalTime}ms`, "success");
                                // FIX 2: RETURN the correct result immediately
                                return { content: cleanExtractedText(retryText), error: null };
                            }
                        }
                    }
                    addLogEntry(`Page ${pageNumber}: Still getting descriptions instead of text after retry`, "warning");
                    // --- END: CORRECTED RETRY BLOCK ---
                }

                // This part now correctly handles the original success case,
                // or the case where the initial response was bad and the retry failed.
                const cleanedFinalText = cleanExtractedText(extractedText);
                if (cleanedFinalText) {
                    addLogEntry(`Page ${pageNumber}: Extracted ${cleanedFinalText.length} characters in ${totalTime}ms (${(totalTime/1000).toFixed(1)}s)`, "success");
                    return { content: cleanedFinalText, error: null };
                } else {
                    addLogEntry(`Page ${pageNumber}: No text content found in ${totalTime}ms`, "warning");
                    return { content: "", error: null };
                }

            } else {
                throw new Error('Invalid response format from Ollama API');
            }

        } catch (error) {
            lastError = error;
            addLogEntry(`Page ${pageNumber}: Ollama error (attempt ${retries + 1}): ${error.message}`, "warning");
            retries++;

            const RETRY_DELAY = 3000; // Define retry delay
            if (retries < MAX_RETRIES) {
                addLogEntry(`Retrying page ${pageNumber} in ${RETRY_DELAY}ms...`, "info");
                await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
            }
        }
    }

    // All retries failed
    addLogEntry(`Page ${pageNumber}: Failed after ${MAX_RETRIES} attempts with Ollama`, "error");
    return { content: "", error: lastError?.message || "Unknown error" };
}


/**
 * Extracts text from an image using Mistral Vision API
 */
async function extractTextWithMistral(imageBlob, pageNumber) {
    addLogEntry(`Analyzing page ${pageNumber} with Mistral Vision...`, "info");

    let retries = 0;
    let lastError = null;

    while (retries < MAX_RETRIES) {
        try {
            // Convert image to base64
            const base64Image = await imageToBase64(imageBlob);

            // Prepare the request payload
            const payload = {
                model: config.mistralModel,
                messages: [
                    {
                        role: "user",
                        content: [
                            {
                                type: "text",
                                text: `Please extract all text from this image of page ${pageNumber}. Return only the text content without any additional formatting or explanations. If there is no text, return an empty response. This is page ${pageNumber} of a PDF document.`
                            },
                            {
                                type: "image_url",
                                image_url: {
                                    url: `data:image/png;base64,${base64Image}`
                                }
                            }
                        ]
                    }
                ],
                max_tokens: 4000,
                temperature: 0.1 // Low temperature for consistent text extraction
            };

            const response = await fetch(`${config.mistralUrl}/v1/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...(config.mistralApiKey && { 'Authorization': `Bearer ${config.mistralApiKey}` })
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.choices && result.choices[0] && result.choices[0].message) {
                const extractedText = result.choices[0].message.content.trim();

                if (extractedText) {
                    addLogEntry(`Page ${pageNumber}: Extracted ${extractedText.length} characters`, "success");
                    return { content: extractedText, error: null };
                } else {
                    addLogEntry(`Page ${pageNumber}: No text content found`, "warning");
                    return { content: "", error: null };
                }
            } else {
                throw new Error('Invalid response format from Mistral API');
            }

        } catch (error) {
            lastError = error;
            addLogEntry(`Page ${pageNumber}: Error (attempt ${retries + 1}): ${error.message}`, "warning");

            if (error.message.includes('429') || error.message.includes('rate limit')) {
                retries++;
                if (retries < MAX_RETRIES) {
                    addLogEntry(`Page ${pageNumber}: Rate limited, waiting ${API_RETRY_WAIT_SECONDS}s...`, "info");
                    await new Promise(resolve => setTimeout(resolve, API_RETRY_WAIT_SECONDS * 1000));
                    continue;
                }
            } else if (error.message.includes('401') || error.message.includes('403')) {
                return { content: null, error: "Authentication failed. Check Mistral API key." };
            } else {
                retries++;
                if (retries < MAX_RETRIES) {
                    addLogEntry(`Page ${pageNumber}: Retrying in ${API_RETRY_WAIT_SECONDS}s...`, "info");
                    await new Promise(resolve => setTimeout(resolve, API_RETRY_WAIT_SECONDS * 1000));
                    continue;
                }
            }
        }
    }

    return { content: null, error: `Failed after ${MAX_RETRIES} retries: ${lastError?.message}` };
}

// --- Auto-Start Server Functions ---

/**
 * Checks if Mistral server is running
 */
async function checkMistralServerStatus() {
    try {
        const response = await fetch(`${config.mistralUrl}/v1/models`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                ...(config.mistralApiKey && { 'Authorization': `Bearer ${config.mistralApiKey}` })
            },
            signal: AbortSignal.timeout(5000) // 5 second timeout
        });

        return response.ok;
    } catch (error) {
        return false;
    }
}

/**
 * Attempts to auto-start a local Mistral server
 */
async function autoStartMistralServer() {
    if (isAutoStarting) {
        addLogEntry("Auto-start already in progress...", "info");
        return false;
    }

    isAutoStarting = true;
    addLogEntry("🚀 Auto-starting Mistral server...", "info");
    updateStatus("Starting Mistral server...", "info");

    try {
        // First, try to start with offline vLLM (preferred method)
        const offlineSuccess = await tryStartWithOfflineVLLM();
        if (offlineSuccess) {
            isAutoStarting = false;
            return true;
        }

        // If offline fails, try with mock server
        const mockSuccess = await tryStartWithNode();
        if (mockSuccess) {
            isAutoStarting = false;
            return true;
        }

        // If both fail, show manual instructions
        showManualStartupInstructions();
        isAutoStarting = false;
        return false;

    } catch (error) {
        addLogEntry(`❌ Auto-start failed: ${error.message}`, "error");
        isAutoStarting = false;
        return false;
    }
}

/**
 * Tries to start offline vLLM Mistral server
 */
async function tryStartWithOfflineVLLM() {
    addLogEntry("🖥️ Attempting to start offline vLLM server...", "info");

    try {
        // Try to start offline vLLM server
        const startResponse = await fetch('http://localhost:3001/api/mistral/start-offline', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                port: new URL(config.mistralUrl).port || '8000',
                hfToken: config.huggingfaceToken
            }),
            signal: AbortSignal.timeout(10000) // 10 second timeout
        });

        const result = await startResponse.json();

        if (result.success) {
            addLogEntry("✅ Offline vLLM server starting...", "success");
            addLogEntry("📥 Note: First-time model download may take 15-30 minutes", "info");

            // Wait for server to be ready (longer timeout for model download)
            await waitForServerReady(300000); // 5 minutes timeout
            return true;
        } else if (result.needsInstall) {
            addLogEntry("📦 vLLM not installed. Installing now...", "info");
            const installSuccess = await installVLLM();
            if (installSuccess) {
                // Retry starting after installation
                return await tryStartWithOfflineVLLM();
            }
            return false;
        } else {
            addLogEntry(`Failed to start offline server: ${result.error}`, "warning");
            if (result.suggestion) {
                addLogEntry(`Suggestion: ${result.suggestion}`, "info");
            }
            return false;
        }

    } catch (error) {
        if (error.name === 'AbortError' || error.message.includes('fetch')) {
            addLogEntry("❌ Backend server not available on port 3001", "error");
            addLogEntry("💡 Make sure to start the app with: npm run dev", "info");
            addLogEntry("💡 This starts both the frontend and required backend services", "info");
        } else {
            addLogEntry(`Offline vLLM start failed: ${error.message}`, "warning");
        }
        return false;
    }
}

/**
 * Installs vLLM package
 */
async function installVLLM() {
    addLogEntry("📦 Installing vLLM package (this may take several minutes)...", "info");
    updateAutoStartStatus("Installing vLLM package...", "loading");

    try {
        const installResponse = await fetch('http://localhost:3001/api/mistral/install-vllm', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                hfToken: config.huggingfaceToken
            })
        });

        const result = await installResponse.json();

        if (result.success) {
            addLogEntry("✅ vLLM installed successfully", "success");
            return true;
        } else {
            addLogEntry(`❌ vLLM installation failed: ${result.error}`, "error");
            if (result.output) {
                addLogEntry(`Installation output: ${result.output}`, "info");
            }
            return false;
        }

    } catch (error) {
        addLogEntry(`❌ vLLM installation error: ${error.message}`, "error");
        return false;
    }
}

/**
 * Warms up the Ollama model for faster subsequent requests
 */
async function warmUpOllamaModel() {
    try {
        addLogEntry("🔥 Warming up Ollama model for faster processing...", "info");
        updateAutoStartStatus("Warming up model...", "loading");

        // Create a small test image (1x1 pixel base64)
        const testImage = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA60e6kgAAAABJRU5ErkJggg==";

        const payload = {
            model: config.ollamaModel || "llava:7b",
            prompt: "test",
            images: [testImage],
            stream: false,
            options: {
                temperature: 0.1,
                num_predict: 10,
                num_ctx: 512
            }
        };

        const response = await fetch('http://localhost:11434/api/generate', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        if (response.ok) {
            addLogEntry("✅ Model warmed up successfully", "success");
            updateAutoStartStatus("Model ready for fast processing", "success");
        }
    } catch (error) {
        addLogEntry(`⚠️ Model warm-up failed: ${error.message}`, "warning");
    }
}

/**
 * Checks Ollama status and availability
 */
async function checkOllamaStatus() {
    addLogEntry("🔍 Checking Ollama status...", "info");
    updateAutoStartStatus("Checking Ollama...", "info");

    try {
        // Check if Ollama API is available
        const response = await fetch('http://localhost:11434/api/tags', {
            method: 'GET',
            signal: AbortSignal.timeout(5000)
        });

        if (response.ok) {
            const data = await response.json();
            const models = data.models || [];
            const hasSelectedModel = models.some(model => model.name === config.ollamaModel);
            const hasAnyVisionModel = models.some(model =>
                model.name.includes('llava') ||
                model.name.includes('moondream') ||
                model.name.includes('bakllava')
            );

            if (hasSelectedModel) {
                addLogEntry(`✅ Ollama is running with ${config.ollamaModel} available`, "success");
                updateAutoStartStatus(`${config.ollamaModel} ready`, "success");
                updateStatus(`Ollama ${config.ollamaModel} is ready for PDF processing`, "success");

                // Warm up the model for faster processing
                setTimeout(() => warmUpOllamaModel(), 1000);

            } else if (hasAnyVisionModel) {
                addLogEntry(`⚠️ Ollama is running but ${config.ollamaModel} not found`, "warning");
                updateAutoStartStatus(`${config.ollamaModel} missing`, "warning");
                updateStatus(`Ollama is running but ${config.ollamaModel} is not installed`, "warning");

                const availableModels = models.filter(m =>
                    m.name.includes('llava') ||
                    m.name.includes('moondream') ||
                    m.name.includes('bakllava')
                ).map(m => m.name).join(', ');
                addLogEntry(`Available vision models: ${availableModels}`, "info");
                addLogEntry(`💡 To install ${config.ollamaModel}, run: ollama pull ${config.ollamaModel}`, "info");
            } else {
                addLogEntry("⚠️ Ollama is running but no vision models found", "warning");
                updateAutoStartStatus("No vision models", "warning");
                updateStatus("Ollama is running but no vision models are installed", "warning");
                addLogEntry("💡 To install vision models, run: ollama pull llava:7b", "info");
                addLogEntry("💡 Or try: ollama pull moondream:1.8b (faster but less accurate)", "info");
            }
        } else {
            throw new Error(`Ollama API returned ${response.status}`);
        }
    } catch (error) {
        addLogEntry(`❌ Ollama not available: ${error.message}`, "error");
        updateAutoStartStatus("Ollama not available", "error");
        updateStatus("Ollama is not running. Please start Ollama and ensure vision models are installed.", "error");

        // Provide helpful setup instructions
        addLogEntry("", "info");
        addLogEntry("📋 To set up Ollama:", "info");
        addLogEntry("1. Download from https://ollama.ai/download", "info");
        addLogEntry("2. Install and restart your computer", "info");
        addLogEntry("3. Run: ollama pull llava:7b", "info");
        addLogEntry("4. Restart this application", "info");
        addLogEntry("", "info");
        addLogEntry("💡 Alternative: Switch to Azure or Mistral OCR engine", "info");
    }
}

/**
 * Tries to start a simple Node.js mock server
 */
async function tryStartWithNode() {
    addLogEntry("🟢 Attempting to start Node.js mock server...", "info");

    try {
        const response = await fetch('http://localhost:3001/api/mistral/start-mock', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                port: new URL(config.mistralUrl).port || '8000'
            }),
            signal: AbortSignal.timeout(10000) // 10 second timeout
        });

        if (response.ok) {
            addLogEntry("✅ Mock server started successfully", "success");
            addLogEntry("⚠️ Note: Using mock server for testing. Install Mistral for full functionality.", "warning");

            await waitForServerReady();
            return true;
        } else {
            addLogEntry("Failed to start mock server", "warning");
            return false;
        }

    } catch (error) {
        if (error.name === 'AbortError' || error.message.includes('fetch')) {
            addLogEntry("❌ Backend server not available on port 3001", "error");
            addLogEntry("💡 Make sure to start the app with: npm run dev", "info");
            addLogEntry("💡 This starts both the frontend and required backend services", "info");
            addLogEntry("", "info");
            addLogEntry("🔄 Alternative: Use Ollama (no backend required)", "info");
            addLogEntry("1. Download Ollama from https://ollama.ai/download", "info");
            addLogEntry("2. Run: ollama pull llava:7b", "info");
            addLogEntry("3. Switch OCR Engine to 'Ollama Vision'", "info");
        } else {
            addLogEntry(`Mock server start failed: ${error.message}`, "warning");
        }
        return false;
    }
}

/**
 * Waits for the server to be ready
 */
async function waitForServerReady(maxWaitTime = 30000) {
    const startTime = Date.now();
    const checkInterval = 2000; // Check every 2 seconds

    addLogEntry("⏳ Waiting for server to be ready...", "info");

    while (Date.now() - startTime < maxWaitTime) {
        const isReady = await checkMistralServerStatus();
        if (isReady) {
            addLogEntry("✅ Server is ready!", "success");
            updateStatus("Mistral server started successfully", "success");
            return true;
        }

        await new Promise(resolve => setTimeout(resolve, checkInterval));
        addLogEntry("Still waiting for server...", "info");
    }

    addLogEntry("⏰ Timeout waiting for server to start", "warning");
    return false;
}

/**
 * Shows manual startup instructions
 */
function showManualStartupInstructions() {
    addLogEntry("📋 Manual setup required. Please follow these steps:", "info");
    addLogEntry("", "info");
    addLogEntry("🐍 Python + vLLM option (Recommended):", "info");
    addLogEntry("1. Install Python 3.8+ from https://python.org/downloads/", "info");
    addLogEntry("2. pip install vllm[vision] torch torchvision", "info");
    addLogEntry("3. Get HuggingFace token from https://huggingface.co/settings/tokens", "info");
    addLogEntry("4. Accept license at https://huggingface.co/mistralai/Pixtral-12B-2409", "info");
    addLogEntry("5. Click 'Setup Real Mistral' button above", "info");
    addLogEntry("", "info");
    addLogEntry("🔄 Alternative: Use mock server for testing", "info");
    addLogEntry("The mock server provides realistic responses for development", "info");
    addLogEntry("", "info");
    addLogEntry("📖 See REAL_MISTRAL_SETUP.md for detailed instructions", "info");

    updateStatus("Manual Mistral setup required. See conversion log for Python/vLLM installation steps.", "warning");
}

/**
 * Updates the auto-start status indicator
 */
function updateAutoStartStatus(message, type = 'info', show = true) {
    // Choose the appropriate status element based on current OCR engine
    let statusElement;
    if (config.ocrEngine === 'ollama' && dom.ollamaAutoStartStatus) {
        statusElement = dom.ollamaAutoStartStatus;
    } else if (dom.mistralAutoStartStatus) {
        statusElement = dom.mistralAutoStartStatus;
    } else {
        return;
    }
    const iconElement = statusElement.querySelector('.status-icon');
    const textElement = statusElement.querySelector('.status-text');

    if (show) {
        statusElement.style.display = 'block';

        // Update icon based on type
        const icons = {
            info: '🚀',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            loading: '⏳'
        };

        if (iconElement) {
            iconElement.textContent = icons[type] || icons.info;
        }

        if (textElement) {
            textElement.textContent = message;
        }

        // Update CSS class for styling
        statusElement.className = `auto-start-status ${type}`;

        // Auto-hide success messages after 5 seconds
        if (type === 'success') {
            setTimeout(() => {
                updateAutoStartStatus('', 'info', false);
            }, 5000);
        }
    } else {
        statusElement.style.display = 'none';
    }
}

/**
 * Main function to check and start Mistral server
 */
async function checkAndStartMistralServer() {
    addLogEntry("🔍 Checking Mistral server status...", "info");
    updateAutoStartStatus("Checking Mistral server status...", "loading");

    const isRunning = await checkMistralServerStatus();

    if (isRunning) {
        addLogEntry("✅ Mistral server is already running", "success");
        updateStatus("Mistral server is ready", "success");
        updateAutoStartStatus("Mistral server is ready", "success");
        return true;
    }

    addLogEntry("❌ Mistral server not found. Attempting auto-start...", "warning");
    updateStatus("Mistral server not found. Auto-starting...", "info");
    updateAutoStartStatus("Auto-starting Mistral server...", "loading");

    const started = await autoStartMistralServer();

    if (started) {
        addLogEntry("🎉 Mistral server auto-start successful!", "success");
        updateAutoStartStatus("Mistral server started successfully!", "success");
        return true;
    } else {
        addLogEntry("❌ Auto-start failed. Manual setup required.", "error");
        updateAutoStartStatus("Auto-start failed. Manual setup required.", "error");
        return false;
    }
}

// --- PDF Processing Functions ---

/**
 * Renders a PDF page to canvas and returns image blob
 */
async function renderPageToBlob(page, dpi = 300) {
    const viewport = page.getViewport({ scale: dpi / 72 });
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    canvas.height = viewport.height;
    canvas.width = viewport.width;

    const renderContext = {
        canvasContext: context,
        viewport: viewport
    };

    await page.render(renderContext).promise;
    
    return new Promise(resolve => {
        canvas.toBlob(resolve, `image/${IMAGE_FORMAT.toLowerCase()}`, 0.95);
    });
}

/**
 * Loads and displays PDF preview
 */
async function loadPdfPreview(file) {
    try {
        const arrayBuffer = await file.arrayBuffer();
        const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
        pdfDocument = await loadingTask.promise;
        
        addLogEntry(`PDF loaded: ${pdfDocument.numPages} pages`, "success");
        
        // Render first page for preview
        const page = await pdfDocument.getPage(1);
        const viewport = page.getViewport({ scale: 1.0 });
        
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
            canvasContext: context,
            viewport: viewport
        };

        await page.render(renderContext).promise;
        
        // Display in preview container
        dom.pdfPreviewContainer.innerHTML = '';
        dom.pdfPreviewContainer.appendChild(canvas);
        
        // Update file details
        dom.pdfFileDetails.innerHTML = `
            <strong>File:</strong> ${file.name}<br>
            <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
            <strong>Pages:</strong> ${pdfDocument.numPages}
        `;
        
        updateButtonStates();
        
    } catch (error) {
        addLogEntry(`Error loading PDF: ${error.message}`, "error");
        updateStatus(`Failed to load PDF: ${error.message}`, "error");
    }
}

/**
 * Updates button states based on current state
 */
function updateButtonStates() {
    const hasAzureConfig = config.azureEndpoint && config.azureKey;
    const hasMistralConfig = config.mistralUrl; // Mistral API key is optional
    const hasOllamaConfig = config.ollamaUrl; // Ollama should be running locally

    let hasConfig;
    if (config.ocrEngine === 'azure') {
        hasConfig = hasAzureConfig;
    } else if (config.ocrEngine === 'ollama') {
        hasConfig = hasOllamaConfig;
    } else {
        hasConfig = hasMistralConfig;
    }

    const hasPdf = currentPdfFile && pdfDocument;

    if (dom.testAzureConnectionBtn) {
        dom.testAzureConnectionBtn.disabled = !hasAzureConfig || isProcessing;
    }

    if (dom.testMistralConnectionBtn) {
        dom.testMistralConnectionBtn.disabled = !hasMistralConfig || isProcessing;
    }

    if (dom.startConversionBtn) {
        dom.startConversionBtn.disabled = !hasConfig || !hasPdf || isProcessing;
    }

    if (dom.downloadResultBtn) {
        dom.downloadResultBtn.disabled = conversionResults.length === 0 || isProcessing;
    }
}

/**
 * Updates the visibility of configuration groups based on selected OCR engine
 */
function updateConfigVisibility() {
    if (dom.azureConfigGroup && dom.mistralConfigGroup) {
        if (config.ocrEngine === 'azure') {
            dom.azureConfigGroup.style.display = 'block';
            dom.mistralConfigGroup.style.display = 'none';
            // Hide Ollama config if it exists
            if (dom.ollamaConfigGroup) dom.ollamaConfigGroup.style.display = 'none';
        } else if (config.ocrEngine === 'ollama') {
            dom.azureConfigGroup.style.display = 'none';
            dom.mistralConfigGroup.style.display = 'none';
            // Show Ollama config if it exists
            if (dom.ollamaConfigGroup) dom.ollamaConfigGroup.style.display = 'block';
        } else {
            dom.azureConfigGroup.style.display = 'none';
            dom.mistralConfigGroup.style.display = 'block';
            // Hide Ollama config if it exists
            if (dom.ollamaConfigGroup) dom.ollamaConfigGroup.style.display = 'none';
        }
    }
}

// --- Event Handlers ---

/**
 * Handles file input change
 */
async function handleFileInput(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    if (!file.name.toLowerCase().endsWith('.pdf')) {
        updateStatus("Please select a PDF file.", "error");
        return;
    }
    
    currentPdfFile = file;
    dom.pdfToTextFileInfo.textContent = `PDF loaded: ${file.name}`;
    
    await loadPdfPreview(file);
}

/**
 * Handles configuration changes
 */
function handleConfigChange() {
    const previousEngine = config.ocrEngine;

    config.ocrEngine = dom.ocrEngineSelect?.value || 'ollama';
    // Remove trailing slash from Azure endpoint to prevent double slashes in URLs
    config.azureEndpoint = dom.azureDiEndpoint?.value?.trim().replace(/\/$/, '') || '';
    config.azureKey = dom.azureDiKey?.value?.trim() || '';
    config.mistralUrl = dom.mistralUrl?.value?.trim() || 'http://localhost:8000';
    config.mistralApiKey = dom.mistralApiKey?.value?.trim() || '';
    config.mistralModel = dom.mistralModel?.value || 'mistral-large-latest';
    config.ollamaUrl = dom.ollamaUrl?.value?.trim() || 'http://localhost:11434';
    config.ollamaModel = dom.ollamaModel?.value || 'llava:7b';
    config.fastMode = dom.ollamaFastModeCheckbox?.checked !== false;
    config.huggingfaceToken = dom.huggingfaceToken?.value?.trim() || '';
    config.imageDpi = parseInt(dom.imageDpiSelect?.value) || 300;
    config.saveImages = dom.saveImagesCheckbox?.checked || false;

    updateConfigVisibility();
    updateButtonStates();

    // Auto-start servers if switching engines
    if (config.ocrEngine === 'mistral' && previousEngine !== 'mistral') {
        setTimeout(async () => {
            await checkAndStartMistralServer();
        }, 500); // Small delay to let UI update
    } else if (config.ocrEngine === 'ollama' && previousEngine !== 'ollama') {
        setTimeout(async () => {
            await checkOllamaStatus();
        }, 500); // Small delay to let UI update
    }
}

/**
 * Handles Azure connection test
 */
async function handleTestConnection() {
    if (isProcessing) return;

    isProcessing = true;
    updateButtonStates();

    const success = await testAzureConnection();

    // Add visual indicator
    const indicator = document.createElement('span');
    indicator.className = `connection-status ${success ? 'connected' : 'disconnected'}`;

    const existingIndicator = dom.testAzureConnectionBtn.querySelector('.connection-status');
    if (existingIndicator) {
        existingIndicator.remove();
    }
    dom.testAzureConnectionBtn.appendChild(indicator);

    isProcessing = false;
    updateButtonStates();
}

/**
 * Handles Mistral connection test
 */
async function handleTestMistralConnection() {
    if (isProcessing) return;

    isProcessing = true;
    updateButtonStates();

    const success = await testMistralConnection();

    // Add visual indicator
    const indicator = document.createElement('span');
    indicator.className = `connection-status ${success ? 'connected' : 'disconnected'}`;

    const existingIndicator = dom.testMistralConnectionBtn.querySelector('.connection-status');
    if (existingIndicator) {
        existingIndicator.remove();
    }
    dom.testMistralConnectionBtn.appendChild(indicator);

    isProcessing = false;
    updateButtonStates();
}

/**
 * Handles real Mistral setup
 */
async function handleSetupRealMistral() {
    if (isProcessing) return;

    isProcessing = true;
    updateButtonStates();
    updateAutoStartStatus("Setting up real Mistral AI...", "loading");

    try {
        addLogEntry("🚀 Starting real Mistral setup...", "info");

        // Step 1: Check system requirements
        const requirements = await checkSystemRequirements();
        addLogEntry(`Python available: ${requirements.python ? '✅' : '❌'}`, requirements.python ? "success" : "error");
        addLogEntry(`vLLM installed: ${requirements.vllm ? '✅' : '❌'}`, requirements.vllm ? "success" : "warning");

        // Step 2: Setup HuggingFace authentication if token provided
        if (config.huggingfaceToken) {
            addLogEntry("🔑 Setting up HuggingFace authentication...", "info");
            const authResult = await setupHuggingFaceAuth(config.huggingfaceToken);
            if (!authResult.success) {
                throw new Error(`HuggingFace authentication failed: ${authResult.error}`);
            }
            addLogEntry("✅ HuggingFace authentication successful", "success");
        } else {
            addLogEntry("⚠️ No HuggingFace token provided. You may need to accept model license manually.", "warning");
        }

        // Step 3: Start offline Mistral server
        addLogEntry("🖥️ Starting offline Mistral Pixtral-12B server (this may take 15-30 minutes for first download)...", "info");
        const startResult = await startOfflineMistralServer();

        if (startResult.success) {
            addLogEntry(`🎉 Real Mistral setup successful! Running in ${startResult.runtime} mode.`, "success");
            updateAutoStartStatus("Real Mistral AI ready!", "success");
            updateStatus("Real Mistral AI is ready for high-accuracy OCR", "success");
        } else {
            throw new Error(startResult.error);
        }

    } catch (error) {
        addLogEntry(`❌ Real Mistral setup failed: ${error.message}`, "error");
        updateAutoStartStatus("Setup failed. Using mock mode.", "warning");
        updateStatus(`Real Mistral setup failed: ${error.message}`, "error");

        // Fall back to mock server
        addLogEntry("🔄 Falling back to mock server...", "info");
        await autoStartMistralServer();
    } finally {
        isProcessing = false;
        updateButtonStates();
    }
}

/**
 * Handles system requirements check
 */
async function handleCheckRequirements() {
    if (isProcessing) return;

    isProcessing = true;
    updateButtonStates();

    try {
        addLogEntry("🔍 Checking system requirements for real Mistral...", "info");

        const requirements = await checkSystemRequirements();

        addLogEntry("📋 System Requirements Report:", "info");
        addLogEntry(`Docker: ${requirements.docker ? '✅ Available' : '❌ Not found'}`, requirements.docker ? "success" : "error");
        addLogEntry(`NVIDIA GPU: ${requirements.nvidia ? '✅ Available' : '⚠️ Not detected (CPU mode will be used)'}`, requirements.nvidia ? "success" : "warning");
        addLogEntry(`Disk Space: ${requirements.disk || 'Unknown'}`, "info");

        if (requirements.recommendations && requirements.recommendations.length > 0) {
            addLogEntry("", "info");
            addLogEntry("📝 Recommendations:", "info");
            requirements.recommendations.forEach(rec => {
                const type = rec.type === 'critical' ? 'error' : rec.type === 'warning' ? 'warning' : 'info';
                addLogEntry(`${rec.type.toUpperCase()}: ${rec.message}`, type);
                addLogEntry(`Action: ${rec.action}`, "info");
                addLogEntry("", "info");
            });
        }

        const readyForReal = requirements.docker;
        updateStatus(
            readyForReal ?
                "System ready for real Mistral deployment!" :
                "System needs setup for real Mistral. See requirements above.",
            readyForReal ? "success" : "warning"
        );

    } catch (error) {
        addLogEntry(`❌ Requirements check failed: ${error.message}`, "error");
        updateStatus(`Requirements check failed: ${error.message}`, "error");
    } finally {
        isProcessing = false;
        updateButtonStates();
    }
}

/**
 * Tests Ollama connection
 */
async function testOllamaConnection() {
    addLogEntry("🔍 Testing Ollama connection...", "info");
    updateStatus("Testing Ollama connection...", "info");

    try {
        const response = await fetch(`${config.ollamaUrl}/api/tags`, {
            method: 'GET',
            signal: AbortSignal.timeout(10000)
        });

        if (response.ok) {
            const data = await response.json();
            const models = data.models || [];
            const hasSelectedModel = models.some(model => model.name === config.ollamaModel);

            if (hasSelectedModel) {
                addLogEntry(`✅ Ollama connection successful! Model ${config.ollamaModel} is available.`, "success");
                updateStatus(`Ollama connection successful with ${config.ollamaModel}`, "success");
            } else {
                addLogEntry(`⚠️ Ollama connected but model ${config.ollamaModel} not found.`, "warning");
                updateStatus(`Model ${config.ollamaModel} not installed in Ollama`, "warning");

                const availableModels = models.map(m => m.name).join(', ');
                addLogEntry(`Available models: ${availableModels || 'None'}`, "info");
            }
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    } catch (error) {
        addLogEntry(`❌ Ollama connection failed: ${error.message}`, "error");
        updateStatus("Ollama connection failed", "error");
    }
}

/**
 * Opens Ollama setup instructions
 */
function setupOllama() {
    const instructions = `🚀 Ollama Setup Instructions:

1. Download Ollama from: https://ollama.ai/download/windows
2. Install and restart your terminal
3. Download a vision model:
   ollama pull llava:7b

4. Verify installation:
   ollama list

5. Test the API:
   curl http://localhost:11434/api/tags

Need help? Check the setup guide: OLLAMA_SETUP_GUIDE.md`;

    alert(instructions);
    addLogEntry("📋 Ollama setup instructions displayed", "info");
}

/**
 * Checks system requirements via API
 */
async function checkSystemRequirements() {
    const response = await fetch('http://localhost:3001/api/system/requirements');
    if (!response.ok) {
        throw new Error(`Requirements check failed: ${response.status}`);
    }
    return await response.json();
}

/**
 * Sets up HuggingFace authentication
 */
async function setupHuggingFaceAuth(token) {
    const response = await fetch('http://localhost:3001/api/huggingface/setup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token })
    });
    return await response.json();
}

/**
 * Starts offline Mistral server
 */
async function startOfflineMistralServer() {
    const response = await fetch('http://localhost:3001/api/mistral/start-offline', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            port: new URL(config.mistralUrl).port || '8000',
            hfToken: config.huggingfaceToken
        })
    });
    return await response.json();
}

/**
 * Processes a single page in parallel mode
 */
async function processPageParallel(pageNum, pdfDocument) {
    try {
        const page = await pdfDocument.getPage(pageNum);
        const imageBlob = await renderPageToBlob(page, config.imageDpi);

        if (!imageBlob) {
            return { pageNumber: pageNum, error: "Failed to render page", content: null };
        }

        // Use the selected OCR engine
        let result;
        if (config.ocrEngine === 'azure') {
            result = await extractTextWithAzureDI(imageBlob, pageNum);
        } else if (config.ocrEngine === 'ollama') {
            result = await extractTextWithOllama(imageBlob, pageNum);
        } else {
            result = await extractTextWithMistral(imageBlob, pageNum);
        }

        return {
            pageNumber: pageNum,
            content: result.content,
            error: result.error
        };

    } catch (error) {
        return { pageNumber: pageNum, error: error.message, content: null };
    }
}

/**
 * Processes a single page in sequential mode
 */
async function processPageSequential(pageNum, pdfDocument) {
    try {
        const page = await pdfDocument.getPage(pageNum);
        const imageBlob = await renderPageToBlob(page, config.imageDpi);

        if (!imageBlob) {
            addLogEntry(`Page ${pageNum}: Failed to render page`, "error");
            return { pageNumber: pageNum, error: "Failed to render page", content: null };
        }

        // Use the selected OCR engine
        let result;
        if (config.ocrEngine === 'azure') {
            result = await extractTextWithAzureDI(imageBlob, pageNum);
        } else if (config.ocrEngine === 'ollama') {
            result = await extractTextWithOllama(imageBlob, pageNum);
        } else {
            result = await extractTextWithMistral(imageBlob, pageNum);
        }

        return {
            pageNumber: pageNum,
            content: result.content,
            error: result.error
        };

    } catch (error) {
        addLogEntry(`Page ${pageNum}: Unexpected error - ${error.message}`, "error");
        return { pageNumber: pageNum, error: error.message, content: null };
    }
}

/**
 * Handles the main conversion process
 */
async function handleStartConversion() {
    if (isProcessing || !currentPdfFile || !pdfDocument) return;

    isProcessing = true;
    updateButtonStates();
    conversionResults = [];

    addLogEntry("Starting PDF to text conversion...", "info");
    updateStatus("Converting PDF to text...", "info");

    const totalPages = pdfDocument.numPages;
    const startTime = Date.now();

    try {
        // Create Word document content
        let wordContent = `
            <html>
                <head>
                    <meta charset="utf-8">
                    <title>Extracted Text from ${currentPdfFile.name}</title>
                </head>
                <body>
                    <h1>Extracted Text from: ${currentPdfFile.name}</h1>
                    <p><strong>Source PDF:</strong> ${currentPdfFile.name}</p>
                    <p><strong>Processing Date:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>OCR Engine:</strong> ${
                        config.ocrEngine === 'azure' ? `Azure Document Intelligence (${AZURE_OCR_MODEL_ID})` :
                        config.ocrEngine === 'ollama' ? `Ollama Vision (${config.ollamaModel})` :
                        `Mistral Vision (${config.mistralModel})`
                    }</p>
                    <p><strong>Total Pages:</strong> ${totalPages}</p>
                    <hr>
        `;

        // Process pages with parallel processing for Ollama if enabled
        const pageResults = [];

        if (config.ocrEngine === 'ollama' && config.parallelProcessing && config.batchSize > 1) {
            addLogEntry(`🚀 Using parallel processing with batch size ${config.batchSize}`, "info");

            // Process pages in batches
            for (let batchStart = 1; batchStart <= totalPages; batchStart += config.batchSize) {
                const batchEnd = Math.min(batchStart + config.batchSize - 1, totalPages);
                const batchPromises = [];

                addLogEntry(`Processing batch: pages ${batchStart}-${batchEnd}`, "info");
                updateProgress((batchStart - 1) / totalPages * 100, `Processing pages ${batchStart}-${batchEnd} in parallel...`);

                // Create promises for this batch
                for (let pageNum = batchStart; pageNum <= batchEnd; pageNum++) {
                    const pagePromise = processPageParallel(pageNum, pdfDocument);
                    batchPromises.push(pagePromise);
                }

                // Wait for all pages in this batch to complete
                const batchResults = await Promise.all(batchPromises);
                pageResults.push(...batchResults);
            }
        } else {
            // Sequential processing (original method)
            for (let pageNum = 1; pageNum <= totalPages; pageNum++) {
                updateProgress((pageNum - 1) / totalPages * 100, `Processing page ${pageNum} of ${totalPages}...`);
                addLogEntry(`Processing page ${pageNum}/${totalPages}...`, "info");

                const result = await processPageSequential(pageNum, pdfDocument);
                pageResults.push(result);
            }
        }

        // Sort results by page number and build word content
        pageResults.sort((a, b) => a.pageNumber - b.pageNumber);

        for (const result of pageResults) {
            if (result.error) {
                // Log errors but don't include them in the final document
                addLogEntry(`Page ${result.pageNumber}: ${result.error}`, "error");
            } else if (result.content) {
                const cleanedText = cleanExtractedText(result.content);
                const paragraphs = formatTextToParagraphs(cleanedText);

                // Add content directly without page markers
                if (paragraphs.length > 0) {
                    paragraphs.forEach(paragraph => {
                        wordContent += `<p>${paragraph.replace(/\n/g, '<br>')}</p>`;
                    });
                }

                conversionResults.push({
                    pageNumber: result.pageNumber,
                    content: cleanedText,
                    paragraphs: paragraphs
                });
            }
            // Skip pages with no content entirely (no markers added)
        }

        wordContent += `
                    <hr>
                    <p><em>Processing completed in ${((Date.now() - startTime) / 1000).toFixed(2)} seconds</em></p>
                </body>
            </html>
        `;

        // Store the Word document content for download
        window.pdfConversionResult = wordContent;

        updateProgress(100, "Conversion completed!");
        addLogEntry(`Conversion completed successfully. Processed ${totalPages} pages in ${((Date.now() - startTime) / 1000).toFixed(2)} seconds.`, "success");
        updateStatus("Conversion completed. Ready to download.", "success");

    } catch (error) {
        addLogEntry(`Conversion failed: ${error.message}`, "error");
        updateStatus(`Conversion failed: ${error.message}`, "error");
    } finally {
        isProcessing = false;
        updateButtonStates();
    }
}

/**
 * Handles downloading the conversion result
 */
function handleDownloadResult() {
    if (!window.pdfConversionResult) {
        updateStatus("No conversion result available.", "error");
        return;
    }

    try {
        // Convert HTML to DOCX using html-docx-js
        const docxBlob = htmlDocx.asBlob(window.pdfConversionResult);

        // Create download link
        const url = URL.createObjectURL(docxBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = currentPdfFile.name.replace('.pdf', '_extracted_text.docx');
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        addLogEntry("Word document downloaded successfully", "success");
        updateStatus("Document downloaded successfully.", "success");

    } catch (error) {
        addLogEntry(`Download failed: ${error.message}`, "error");
        updateStatus(`Download failed: ${error.message}`, "error");
    }
}

// --- Initialization ---

/**
 * Initializes the PDF to Text Converter module
 */
export function initPdfToTextConverter() {
    console.log("Initializing PDF to Text Converter...");
    
    // Load configuration from environment variables
    config.azureEndpoint = import.meta.env?.VITE_AZURE_DI_ENDPOINT || '';
    config.azureKey = import.meta.env?.VITE_AZURE_DI_KEY || '';
    config.mistralUrl = import.meta.env?.VITE_MISTRAL_URL || 'http://localhost:8000';
    config.mistralApiKey = import.meta.env?.VITE_MISTRAL_API_KEY || '';
    config.mistralModel = import.meta.env?.VITE_MISTRAL_MODEL || 'mistral-large-latest';

    // Set initial values for Azure
    if (dom.azureDiEndpoint && config.azureEndpoint) {
        dom.azureDiEndpoint.value = config.azureEndpoint;
    }
    if (dom.azureDiKey && config.azureKey) {
        dom.azureDiKey.value = config.azureKey;
    }

    // Set initial values for Mistral
    if (dom.mistralUrl && config.mistralUrl) {
        dom.mistralUrl.value = config.mistralUrl;
    }
    if (dom.mistralApiKey && config.mistralApiKey) {
        dom.mistralApiKey.value = config.mistralApiKey;
    }
    if (dom.mistralModel && config.mistralModel) {
        dom.mistralModel.value = config.mistralModel;
    }

    // Set initial values for Ollama
    if (dom.ollamaUrl && config.ollamaUrl) {
        dom.ollamaUrl.value = config.ollamaUrl;
    }
    if (dom.ollamaModel && config.ollamaModel) {
        dom.ollamaModel.value = config.ollamaModel;
    }
    
    // Set up event listeners
    if (dom.pdfFileInput) {
        dom.pdfFileInput.addEventListener('change', handleFileInput);
    }

    if (dom.ocrEngineSelect) {
        dom.ocrEngineSelect.addEventListener('change', handleConfigChange);
    }

    if (dom.azureDiEndpoint) {
        dom.azureDiEndpoint.addEventListener('input', handleConfigChange);
    }

    if (dom.azureDiKey) {
        dom.azureDiKey.addEventListener('input', handleConfigChange);
    }

    if (dom.mistralUrl) {
        dom.mistralUrl.addEventListener('input', handleConfigChange);
    }

    if (dom.mistralApiKey) {
        dom.mistralApiKey.addEventListener('input', handleConfigChange);
    }

    if (dom.mistralModel) {
        dom.mistralModel.addEventListener('change', handleConfigChange);
    }

    if (dom.imageDpiSelect) {
        dom.imageDpiSelect.addEventListener('change', handleConfigChange);
    }

    if (dom.saveImagesCheckbox) {
        dom.saveImagesCheckbox.addEventListener('change', handleConfigChange);
    }

    if (dom.testAzureConnectionBtn) {
        dom.testAzureConnectionBtn.addEventListener('click', handleTestConnection);
    }

    if (dom.testMistralConnectionBtn) {
        dom.testMistralConnectionBtn.addEventListener('click', handleTestMistralConnection);
    }

    if (dom.setupRealMistralBtn) {
        dom.setupRealMistralBtn.addEventListener('click', handleSetupRealMistral);
    }

    if (dom.checkRequirementsBtn) {
        dom.checkRequirementsBtn.addEventListener('click', handleCheckRequirements);
    }

    // Ollama event listeners
    if (dom.ollamaUrl) {
        dom.ollamaUrl.addEventListener('input', handleConfigChange);
    }

    if (dom.ollamaModel) {
        dom.ollamaModel.addEventListener('change', handleConfigChange);
    }

    if (dom.ollamaFastModeCheckbox) {
        dom.ollamaFastModeCheckbox.addEventListener('change', handleConfigChange);
    }

    if (dom.testOllamaConnectionBtn) {
        dom.testOllamaConnectionBtn.addEventListener('click', testOllamaConnection);
    }

    if (dom.setupOllamaBtn) {
        dom.setupOllamaBtn.addEventListener('click', setupOllama);
    }

    if (dom.startConversionBtn) {
        dom.startConversionBtn.addEventListener('click', handleStartConversion);
    }

    if (dom.downloadResultBtn) {
        dom.downloadResultBtn.addEventListener('click', handleDownloadResult);
    }
    
    // Initialize PDF.js worker
    if (typeof pdfjsLib !== 'undefined') {
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    }
    
    // Initialize UI state
    handleConfigChange();
    updateButtonStates();
    updateStatus("PDF to Text Converter Ready.", "info");
    addLogEntry("PDF to Text Converter initialized", "info");

    // Show initial setup guidance
    addLogEntry("", "info");
    addLogEntry("📋 Quick Setup Guide:", "info");
    addLogEntry("• Ollama (Recommended): Download from https://ollama.ai/download", "info");
    addLogEntry("• Azure: Requires Azure Document Intelligence subscription", "info");
    addLogEntry("• Mistral: Requires backend server (auto-started with npm dev)", "info");
    addLogEntry("", "info");

    // Auto-start servers based on selected engine
    setTimeout(async () => {
        if (config.ocrEngine === 'mistral') {
            await checkAndStartMistralServer();
        } else if (config.ocrEngine === 'ollama') {
            await checkOllamaStatus();
        }
    }, 1000); // Small delay to let UI settle

    console.log("PDF to Text Converter initialized successfully.");
}