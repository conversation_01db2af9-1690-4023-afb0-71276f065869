<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Navigation Test</title>
    <style>
        .nav-button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        .nav-button.active {
            background: #0056b3;
        }
        .view-container {
            padding: 20px;
            border: 1px solid #ccc;
            margin: 10px 0;
            display: none;
        }
        .view-container.active {
            display: block;
        }
    </style>
</head>
<body>
    <h1>Tab Navigation Test</h1>
    
    <div class="navigation">
        <button id="tab1-btn" class="nav-button active">Tab 1</button>
        <button id="tab2-btn" class="nav-button">Tab 2</button>
        <button id="tab3-btn" class="nav-button">Tab 3</button>
    </div>
    
    <div id="tab1-content" class="view-container active">
        <h2>Tab 1 Content</h2>
        <p>This is the content for tab 1.</p>
    </div>
    
    <div id="tab2-content" class="view-container">
        <h2>Tab 2 Content</h2>
        <p>This is the content for tab 2.</p>
    </div>
    
    <div id="tab3-content" class="view-container">
        <h2>Tab 3 Content</h2>
        <p>This is the content for tab 3.</p>
    </div>
    
    <script>
        console.log('Tab navigation test script loaded');
        
        // Test basic tab functionality
        const tabs = [
            { button: document.getElementById('tab1-btn'), content: document.getElementById('tab1-content') },
            { button: document.getElementById('tab2-btn'), content: document.getElementById('tab2-content') },
            { button: document.getElementById('tab3-btn'), content: document.getElementById('tab3-content') }
        ];
        
        function switchTab(activeIndex) {
            console.log('Switching to tab:', activeIndex);
            
            // Remove active class from all buttons and hide all content
            tabs.forEach((tab, index) => {
                tab.button.classList.remove('active');
                tab.content.classList.remove('active');
            });
            
            // Add active class to selected button and show content
            tabs[activeIndex].button.classList.add('active');
            tabs[activeIndex].content.classList.add('active');
        }
        
        // Add click listeners
        tabs.forEach((tab, index) => {
            tab.button.addEventListener('click', () => {
                console.log('Tab button clicked:', index);
                switchTab(index);
            });
        });
        
        console.log('Tab navigation test setup complete');
    </script>
</body>
</html>
