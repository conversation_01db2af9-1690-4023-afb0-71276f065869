<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF to Text Converter Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .config-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .config-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .config-section input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        .status-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .log-entry {
            margin-bottom: 3px;
        }
        .log-entry.info { color: #0c5460; }
        .log-entry.success { color: #155724; }
        .log-entry.warning { color: #856404; }
        .log-entry.error { color: #721c24; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 PDF to Text Converter Test</h1>
        <p>This page tests the PDF to Text Converter functionality independently.</p>
        
        <div class="test-result info">
            <h3>📋 Configuration</h3>
            <div class="config-section">
                <label for="test-azure-endpoint">Azure DI Endpoint:</label>
                <input type="text" id="test-azure-endpoint" placeholder="https://your-resource.cognitiveservices.azure.com/" />
                
                <label for="test-azure-key">Azure DI Key:</label>
                <input type="password" id="test-azure-key" placeholder="Your Azure Document Intelligence Key" />
                
                <button onclick="testAzureConnection()">Test Azure Connection</button>
                <span id="connection-status"></span>
            </div>
        </div>
        
        <div class="test-result info">
            <h3>📄 PDF Processing</h3>
            <input type="file" id="test-pdf-input" accept=".pdf" />
            <button onclick="loadPdfTest()" disabled id="load-pdf-btn">Load PDF</button>
            <button onclick="testPdfRendering()" disabled id="test-render-btn">Test PDF Rendering</button>
            <div id="pdf-info"></div>
        </div>
        
        <div class="test-result info">
            <h3>📊 Test Results</h3>
            <div id="test-status-log" class="status-log">
                <div class="log-entry info">Ready to test PDF to Text Converter functionality...</div>
            </div>
        </div>
        
        <div class="test-result info">
            <h3>🔗 Quick Links</h3>
            <button onclick="openMainApp()">Open Main Application</button>
            <button onclick="checkDependencies()">Check Dependencies</button>
            <button onclick="testEnvironmentVariables()">Test Environment Variables</button>
        </div>
    </div>

    <!-- Include PDF.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        // Configure PDF.js worker
        if (typeof pdfjsLib !== 'undefined') {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        }
        
        let testPdfDocument = null;
        
        function addLogEntry(message, type = 'info') {
            const log = document.getElementById('test-status-log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }
        
        function updateConnectionStatus(status, type) {
            const statusEl = document.getElementById('connection-status');
            statusEl.textContent = status;
            statusEl.className = type;
        }
        
        async function testAzureConnection() {
            const endpoint = document.getElementById('test-azure-endpoint').value.trim();
            const key = document.getElementById('test-azure-key').value.trim();
            
            if (!endpoint || !key) {
                addLogEntry('Please enter both Azure endpoint and key', 'error');
                updateConnectionStatus('❌ Missing credentials', 'error');
                return;
            }
            
            addLogEntry('Testing Azure connection...', 'info');
            updateConnectionStatus('🔄 Testing...', 'info');
            
            try {
                const response = await fetch(`${endpoint}/documentintelligence/documentModels?api-version=2023-07-31`, {
                    method: 'GET',
                    headers: {
                        'Ocp-Apim-Subscription-Key': key,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    addLogEntry('Azure connection successful!', 'success');
                    updateConnectionStatus('✅ Connected', 'success');
                    document.getElementById('load-pdf-btn').disabled = false;
                } else {
                    addLogEntry(`Azure connection failed: ${response.status} ${response.statusText}`, 'error');
                    updateConnectionStatus('❌ Connection failed', 'error');
                }
            } catch (error) {
                addLogEntry(`Azure connection error: ${error.message}`, 'error');
                updateConnectionStatus('❌ Connection error', 'error');
            }
        }
        
        async function loadPdfTest() {
            const fileInput = document.getElementById('test-pdf-input');
            const file = fileInput.files[0];
            
            if (!file) {
                addLogEntry('Please select a PDF file first', 'warning');
                return;
            }
            
            if (!file.name.toLowerCase().endsWith('.pdf')) {
                addLogEntry('Please select a valid PDF file', 'error');
                return;
            }
            
            addLogEntry(`Loading PDF: ${file.name}`, 'info');
            
            try {
                const arrayBuffer = await file.arrayBuffer();
                const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
                testPdfDocument = await loadingTask.promise;
                
                addLogEntry(`PDF loaded successfully: ${testPdfDocument.numPages} pages`, 'success');
                
                document.getElementById('pdf-info').innerHTML = `
                    <strong>File:</strong> ${file.name}<br>
                    <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                    <strong>Pages:</strong> ${testPdfDocument.numPages}
                `;
                
                document.getElementById('test-render-btn').disabled = false;
                
            } catch (error) {
                addLogEntry(`Error loading PDF: ${error.message}`, 'error');
            }
        }
        
        async function testPdfRendering() {
            if (!testPdfDocument) {
                addLogEntry('No PDF loaded', 'error');
                return;
            }
            
            addLogEntry('Testing PDF page rendering...', 'info');
            
            try {
                const page = await testPdfDocument.getPage(1);
                const viewport = page.getViewport({ scale: 1.0 });
                
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                
                const renderContext = {
                    canvasContext: context,
                    viewport: viewport
                };
                
                await page.render(renderContext).promise;
                
                // Convert to blob to test image generation
                canvas.toBlob((blob) => {
                    if (blob) {
                        addLogEntry(`Page rendered successfully. Image size: ${(blob.size / 1024).toFixed(2)} KB`, 'success');
                    } else {
                        addLogEntry('Failed to generate image blob', 'error');
                    }
                }, 'image/png', 0.95);
                
            } catch (error) {
                addLogEntry(`Error rendering PDF page: ${error.message}`, 'error');
            }
        }
        
        function checkDependencies() {
            addLogEntry('Checking dependencies...', 'info');
            
            // Check PDF.js
            if (typeof pdfjsLib !== 'undefined') {
                addLogEntry('✅ PDF.js loaded successfully', 'success');
            } else {
                addLogEntry('❌ PDF.js not loaded', 'error');
            }
            
            // Check html-docx-js (if available)
            if (typeof htmlDocx !== 'undefined') {
                addLogEntry('✅ html-docx-js available', 'success');
            } else {
                addLogEntry('⚠️ html-docx-js not available (normal for this test page)', 'warning');
            }
            
            // Check Fetch API
            if (typeof fetch !== 'undefined') {
                addLogEntry('✅ Fetch API available', 'success');
            } else {
                addLogEntry('❌ Fetch API not available', 'error');
            }
        }
        
        function testEnvironmentVariables() {
            addLogEntry('Testing environment variable access...', 'info');
            
            // In a real Vite environment, these would be available
            if (typeof import !== 'undefined' && import.meta && import.meta.env) {
                addLogEntry('✅ Vite environment detected', 'success');
                
                const azureEndpoint = import.meta.env.VITE_AZURE_DI_ENDPOINT;
                const azureKey = import.meta.env.VITE_AZURE_DI_KEY;
                
                if (azureEndpoint) {
                    addLogEntry('✅ VITE_AZURE_DI_ENDPOINT found', 'success');
                    document.getElementById('test-azure-endpoint').value = azureEndpoint;
                } else {
                    addLogEntry('⚠️ VITE_AZURE_DI_ENDPOINT not set', 'warning');
                }
                
                if (azureKey) {
                    addLogEntry('✅ VITE_AZURE_DI_KEY found', 'success');
                    document.getElementById('test-azure-key').value = azureKey;
                } else {
                    addLogEntry('⚠️ VITE_AZURE_DI_KEY not set', 'warning');
                }
            } else {
                addLogEntry('⚠️ Not in Vite environment - environment variables not available', 'warning');
            }
        }
        
        function openMainApp() {
            window.open('http://localhost:8000', '_blank');
        }
        
        // Initialize
        document.getElementById('test-pdf-input').addEventListener('change', function() {
            if (this.files[0]) {
                document.getElementById('load-pdf-btn').disabled = false;
            }
        });
        
        // Auto-check dependencies on load
        setTimeout(checkDependencies, 1000);
    </script>
</body>
</html>
