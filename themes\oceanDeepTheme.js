
export const oceanDeepTheme = {
  name: 'Ocean Deep',
  properties: {
    '--bg-primary': '#0A192F', // Dark Navy
    '--bg-secondary': '#172A45', // Slightly Lighter Navy for secondary elements
    '--bg-app-container': '#0A192F',
    '--bg-panel': '#112240', // Panel Background
    '--bg-header': '#020C1B', // Very Dark Header
    '--bg-status-bar': '#020C1B',
    '--bg-modal-backdrop': 'rgba(2, 12, 27, 0.7)',
    '--bg-modal-content': '#172A45',
    '--bg-input': '#0A192F', // Input background same as primary for seamless look
    '--bg-input-disabled': '#1F3A61',
    '--bg-list-box': '#112240',
    '--bg-list-item-hover': '#1F3A61', // Lighter hover
    '--bg-list-item-selected': '#64FFDA', // Teal accent
    '--bg-ssml-editor-view': '#0A192F',
    '--bg-ssml-instructions': '#172A45',
    '--bg-ssml-preview-modal-content': '#112240',

    '--text-primary': '#CCD6F6', // Light grayish blue
    '--text-secondary': '#8892B0', // Slate gray
    '--text-panel-heading': '#64FFDA', // Teal accent for headings
    '--text-header': '#CCD6F6',
    '--text-status-bar': '#CCD6F6',
    '--text-button-primary': '#0A192F', // Dark text on light buttons
    '--text-list-item-selected': '#0A192F', // Dark text on selected item
    '--text-placeholder': '#4A5C7A',
    '--text-highlighted': '#0A192F', // Dark text on teal highlight
    '--text-search-highlighted': '#0A192F',
    '--text-ssml-editor-file-info': '#8892B0',
    '--text-ssml-instructions': '#CCD6F6',
    '--text-ssml-tag': '#64FFDA', // Teal
    '--text-ssml-attr-name': '#FFD700', // Gold/Yellow
    '--text-ssml-attr-value': '#79CBFF', // Light Blue
    '--text-ssml-comment': '#4A5C7A',

    '--border-primary': '#1F3A61', // Subtle borders
    '--border-secondary': '#2A4A75',
    '--border-input': '#64FFDA', // Teal border for inputs
    '--border-ssml-text-widget': '#64FFDA',
    '--border-ssml-instructions': '#1F3A61',
    '--border-ssml-preview-modal-content': '#1F3A61',

    '--accent-primary': '#64FFDA', // Teal
    '--accent-secondary': '#79CBFF', // Light Blue
    '--accent-secondary-hover': '#6AC0F2', // Darker Light Blue
    '--accent-playback': '#64FFDA', // Teal
    '--accent-playback-hover': '#50E0C0', // Darker Teal
    '--accent-highlight': '#64FFDA', // Teal highlight bg
    '--accent-search-highlight': '#FFD700', // Gold search highlight bg
    '--accent-volume-slider': '#64FFDA',

    '--button-primary-bg': '#64FFDA', // Teal buttons
    '--button-primary-hover-bg': '#79FFEE',
    '--button-primary-active-bg': '#50E0C0',
    '--button-disabled-bg': '#1F3A61',
    '--button-file-input-bg': '#79CBFF', // Light blue for file inputs
    '--button-file-input-hover-bg': '#90DFFF',
    
    '--button-accent-bg': '#64FFDA', // Teal accent buttons
    '--button-accent-hover-bg': '#79FFEE',
    '--button-warning-bg': '#FFD700', // Gold for warning
    '--button-warning-hover-bg': '#FFEE30',
    '--button-info-bg': '#79CBFF', // Light blue for info
    '--button-info-hover-bg': '#90DFFF',
    
    '--shadow-light': 'rgba(2, 12, 27, 0.7)', // Deeper shadows
    '--shadow-medium': 'rgba(2, 12, 27, 0.9)',
    '--shadow-modal': 'rgba(0, 0, 0, 0.3)', // Standard modal shadow
    '--shadow-inset-active-nav': 'rgba(0,0,0,0.3)',

    '--icon-primary': '#64FFDA', // Teal icons
    '--icon-ms-tts': '#79CBFF', // Light Blue for MS TTS icon
    '--icon-selected-item': '#0A192F', // Dark icon on selected item

    '--status-processing-color': '#79CBFF',
    '--status-ok-color': '#64FFDA',
    '--status-flagged-color': '#FFD700',
    '--status-error-color': '#FF6B6B', // Coral Red
    '--status-corrected-color': '#79CBFF',
    '--status-no-text-color': '#4A5C7A',
  }
};