<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Navigation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .status-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .log-entry {
            margin-bottom: 3px;
        }
        .log-entry.info { color: #0c5460; }
        .log-entry.success { color: #155724; }
        .log-entry.warning { color: #856404; }
        .log-entry.error { color: #721c24; }
        .tab-test {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        .tab-test button {
            font-size: 12px;
            padding: 8px 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Tab Navigation Test</h1>
        <p>This page tests the tab navigation functionality and checks if all tabs are properly implemented.</p>
        
        <div class="test-result info">
            <h3>📋 Tab Navigation Test</h3>
            <div class="tab-test">
                <button onclick="testTabExists('audiobook-verification-btn')">Test Audiobook Verification</button>
                <button onclick="testTabExists('audiobook-text-editor-btn')">Test Text Editor</button>
                <button onclick="testTabExists('ai-voice-creator-btn')">Test AI Voice Creator</button>
                <button onclick="testTabExists('quote-footnote-inserter-btn')">Test Quote & Footnote</button>
                <button onclick="testTabExists('pdf-to-text-converter-btn')">Test PDF to Text</button>
            </div>
            <button onclick="testAllTabs()">Test All Tabs</button>
            <button onclick="openMainApp()">Open Main Application</button>
        </div>
        
        <div class="test-result info">
            <h3>📊 Test Results</h3>
            <div id="test-status-log" class="status-log">
                <div class="log-entry info">Ready to test tab navigation...</div>
            </div>
        </div>
        
        <div class="test-result info">
            <h3>🔍 DOM Element Check</h3>
            <button onclick="checkDOMElements()">Check DOM Elements</button>
            <button onclick="checkCSSFiles()">Check CSS Files</button>
            <button onclick="checkJSFiles()">Check JavaScript Files</button>
        </div>
    </div>

    <script>
        function addLogEntry(message, type = 'info') {
            const log = document.getElementById('test-status-log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }
        
        function testTabExists(buttonId) {
            addLogEntry(`Testing tab button: ${buttonId}`, 'info');
            
            // Open main app in iframe to test
            const iframe = document.createElement('iframe');
            iframe.src = 'http://localhost:8000';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            
            iframe.onload = function() {
                try {
                    const button = iframe.contentDocument.getElementById(buttonId);
                    if (button) {
                        addLogEntry(`✅ Tab button '${buttonId}' found`, 'success');
                        
                        // Test if button is clickable
                        if (button.onclick || button.addEventListener) {
                            addLogEntry(`✅ Tab button '${buttonId}' has event listeners`, 'success');
                        } else {
                            addLogEntry(`⚠️ Tab button '${buttonId}' may not have event listeners`, 'warning');
                        }
                        
                        // Check corresponding view container
                        const viewId = buttonId.replace('-btn', '-view-container');
                        const viewContainer = iframe.contentDocument.getElementById(viewId);
                        if (viewContainer) {
                            addLogEntry(`✅ View container '${viewId}' found`, 'success');
                        } else {
                            addLogEntry(`❌ View container '${viewId}' not found`, 'error');
                        }
                    } else {
                        addLogEntry(`❌ Tab button '${buttonId}' not found`, 'error');
                    }
                } catch (error) {
                    addLogEntry(`❌ Error testing '${buttonId}': ${error.message}`, 'error');
                }
                
                document.body.removeChild(iframe);
            };
            
            iframe.onerror = function() {
                addLogEntry(`❌ Failed to load main application for testing '${buttonId}'`, 'error');
                document.body.removeChild(iframe);
            };
        }
        
        function testAllTabs() {
            addLogEntry('Testing all tabs...', 'info');
            const tabs = [
                'audiobook-verification-btn',
                'audiobook-text-editor-btn', 
                'ai-voice-creator-btn',
                'quote-footnote-inserter-btn',
                'pdf-to-text-converter-btn'
            ];
            
            tabs.forEach((tabId, index) => {
                setTimeout(() => testTabExists(tabId), index * 1000);
            });
        }
        
        function checkDOMElements() {
            addLogEntry('Checking DOM elements in main application...', 'info');
            
            const iframe = document.createElement('iframe');
            iframe.src = 'http://localhost:8000';
            iframe.style.display = 'none';
            document.body.appendChild(iframe);
            
            iframe.onload = function() {
                try {
                    const doc = iframe.contentDocument;
                    
                    // Check navigation buttons
                    const navButtons = [
                        'audiobook-verification-btn',
                        'audiobook-text-editor-btn',
                        'ai-voice-creator-btn', 
                        'quote-footnote-inserter-btn',
                        'pdf-to-text-converter-btn'
                    ];
                    
                    navButtons.forEach(buttonId => {
                        const button = doc.getElementById(buttonId);
                        if (button) {
                            addLogEntry(`✅ Navigation button '${buttonId}' exists`, 'success');
                        } else {
                            addLogEntry(`❌ Navigation button '${buttonId}' missing`, 'error');
                        }
                    });
                    
                    // Check view containers
                    const viewContainers = [
                        'app-container',
                        'text-editor-view-container',
                        'ai-voice-creator-view-container',
                        'quote-footnote-inserter-view-container',
                        'pdf-to-text-converter-view-container'
                    ];
                    
                    viewContainers.forEach(containerId => {
                        const container = doc.getElementById(containerId);
                        if (container) {
                            addLogEntry(`✅ View container '${containerId}' exists`, 'success');
                        } else {
                            addLogEntry(`❌ View container '${containerId}' missing`, 'error');
                        }
                    });
                    
                } catch (error) {
                    addLogEntry(`❌ Error checking DOM elements: ${error.message}`, 'error');
                }
                
                document.body.removeChild(iframe);
            };
        }
        
        function checkCSSFiles() {
            addLogEntry('Checking CSS file availability...', 'info');
            
            const cssFiles = [
                'base.css',
                'themes.css',
                'controls.css',
                'verification.css',
                'ai-voice-creator.css',
                'quote-footnote-inserter.css',
                'pdf-to-text-converter.css'
            ];
            
            cssFiles.forEach(cssFile => {
                fetch(`http://localhost:8000/${cssFile}`)
                    .then(response => {
                        if (response.ok) {
                            addLogEntry(`✅ CSS file '${cssFile}' accessible`, 'success');
                        } else {
                            addLogEntry(`❌ CSS file '${cssFile}' returned ${response.status}`, 'error');
                        }
                    })
                    .catch(error => {
                        addLogEntry(`❌ CSS file '${cssFile}' failed to load: ${error.message}`, 'error');
                    });
            });
        }
        
        function checkJSFiles() {
            addLogEntry('Checking JavaScript file availability...', 'info');
            
            const jsFiles = [
                'index.js',
                'domElements.js',
                'ui.js',
                'aiVerification.js',
                'aiVoiceCreator.js',
                'quoteFootnoteInserter.js',
                'pdfToTextConverter.js'
            ];
            
            jsFiles.forEach(jsFile => {
                fetch(`http://localhost:8000/${jsFile}`)
                    .then(response => {
                        if (response.ok) {
                            addLogEntry(`✅ JS file '${jsFile}' accessible`, 'success');
                        } else {
                            addLogEntry(`❌ JS file '${jsFile}' returned ${response.status}`, 'error');
                        }
                    })
                    .catch(error => {
                        addLogEntry(`❌ JS file '${jsFile}' failed to load: ${error.message}`, 'error');
                    });
            });
        }
        
        function openMainApp() {
            window.open('http://localhost:8000', '_blank');
        }
        
        // Auto-run basic checks
        setTimeout(() => {
            addLogEntry('Running automatic checks...', 'info');
            checkCSSFiles();
            setTimeout(checkJSFiles, 2000);
            setTimeout(checkDOMElements, 4000);
        }, 1000);
    </script>
</body>
</html>
