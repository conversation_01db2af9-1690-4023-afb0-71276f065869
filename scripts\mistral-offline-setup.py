#!/usr/bin/env python3

"""
Mistral Offline Setup Script
Downloads the model once, then enables true offline usage
"""

import os
import sys
import argparse
from pathlib import Path
from huggingface_hub import login, snapshot_download
from transformers import AutoProcessor, AutoModelForVision2Seq
import torch

def check_model_exists(model_name="mistralai/Pixtral-12B-2409"):
    """Check if model is already downloaded locally"""
    try:
        # Try to load from cache without internet
        processor = AutoProcessor.from_pretrained(
            model_name, 
            local_files_only=True,
            trust_remote_code=True
        )
        print(f"Model {model_name} found locally")
        return True
    except Exception as e:
        print(f"Model {model_name} not found locally: {str(e)}")
        return False

def download_model(hf_token, model_name="mistralai/Pixtral-12B-2409"):
    """Download model with HuggingFace token"""
    try:
        print(f"Authenticating with HuggingFace...")
        login(token=hf_token)

        print(f"Downloading {model_name}...")
        print("This may take 15-30 minutes (24GB download)")
        
        # Download model files
        snapshot_download(
            repo_id=model_name,
            local_files_only=False,
            resume_download=True
        )
        
        # Verify download by loading processor and model
        print("Verifying download...")
        processor = AutoProcessor.from_pretrained(
            model_name,
            trust_remote_code=True
        )

        # Test model loading (just to verify it works)
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Testing model load on {device}...")
        
        if device == "cuda":
            model = AutoModelForVision2Seq.from_pretrained(
                model_name,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
        else:
            model = AutoModelForVision2Seq.from_pretrained(
                model_name,
                torch_dtype=torch.float32,
                trust_remote_code=True
            )
        
        print("Model downloaded and verified successfully!")
        print("You can now run offline inference without internet or tokens")
        return True
        
    except Exception as e:
        print(f"Download failed: {str(e)}")
        return False

def test_offline_mode(model_name="mistralai/Pixtral-12B-2409"):
    """Test that model works in offline mode"""
    try:
        print("Testing offline mode (no internet required)...")
        
        # Load without internet connection
        processor = AutoProcessor.from_pretrained(
            model_name,
            local_files_only=True,
            trust_remote_code=True
        )
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        
        if device == "cuda":
            model = AutoModelForVision2Seq.from_pretrained(
                model_name,
                torch_dtype=torch.float16,
                device_map="auto",
                local_files_only=True,
                trust_remote_code=True
            )
        else:
            model = AutoModelForVision2Seq.from_pretrained(
                model_name,
                torch_dtype=torch.float32,
                local_files_only=True,
                trust_remote_code=True
            )
        
        print("Offline mode working perfectly!")
        print("Ready for true offline inference")
        return True
        
    except Exception as e:
        print(f"Offline test failed: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Mistral Offline Setup")
    parser.add_argument("--check", action="store_true", help="Check if model exists locally")
    parser.add_argument("--download", help="Download model with HF token")
    parser.add_argument("--test-offline", action="store_true", help="Test offline mode")
    parser.add_argument("--model", default="mistralai/Pixtral-12B-2409", help="Model to use")
    
    args = parser.parse_args()
    
    print("Mistral Offline Setup")
    print("=" * 50)
    
    if args.check:
        print("Checking for local model...")
        exists = check_model_exists(args.model)
        if exists:
            print("Model ready for offline use!")
        else:
            print("Model not found. Use --download to get it.")
        return
    
    if args.download:
        print("Starting model download...")
        success = download_model(args.download, args.model)
        if success:
            print("\nSetup complete!")
            print("You can now run offline inference without internet")
            print("No more tokens needed for inference")
        else:
            print("\nSetup failed")
        return
    
    if args.test_offline:
        print("Testing offline capabilities...")
        success = test_offline_mode(args.model)
        if success:
            print("\nOffline mode confirmed working!")
        else:
            print("\nOffline mode not working")
        return
    
    # Default: show status
    print("Current Status:")
    exists = check_model_exists(args.model)

    if exists:
        print("Model downloaded and ready")
        print("Testing offline mode...")
        test_offline_mode(args.model)
    else:
        print("Model not downloaded yet")
        print("\nNext steps:")
        print("1. Get HuggingFace token: https://huggingface.co/settings/tokens")
        print("2. Accept license: https://huggingface.co/mistralai/Pixtral-12B-2409")
        print("3. Run: python mistral-offline-setup.py --download YOUR_TOKEN")
        print("4. After download: True offline inference (no token needed)")

if __name__ == "__main__":
    main()
