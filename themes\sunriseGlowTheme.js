
export const sunriseGlowTheme = {
  name: 'Sunrise Glow',
  properties: {
    '--bg-primary': '#FFF8E1', // Pale Yellow / Cream
    '--bg-secondary': '#FFFFFF', // White
    '--bg-app-container': '#FFFFFF',
    '--bg-panel': '#FFFDF5', // Very light cream panel
    '--bg-header': '#FF8F00', // <PERSON>er
    '--bg-status-bar': '#E65100', // Dark Orange Status Bar
    '--bg-modal-backdrop': 'rgba(0, 0, 0, 0.4)',
    '--bg-modal-content': '#FFFFFF',
    '--bg-input': '#FFFFFF',
    '--bg-input-disabled': '#FFE0B2', // Light Orange
    '--bg-list-box': '#FFFFFF',
    '--bg-list-item-hover': '#FFF0C0', // Light yellow hover
    '--bg-list-item-selected': '#FFB300', // Darker Amber for selected
    '--bg-ssml-editor-view': '#FFF8E1',
    '--bg-ssml-instructions': '#FFF0C0',
    '--bg-ssml-preview-modal-content': '#FFF5E0',

    '--text-primary': '#4E342E', // Dark Brown
    '--text-secondary': '#795548', // Medium Brown
    '--text-panel-heading': '#E65100', // Dark Orange for headings
    '--text-header': '#FFFFFF',
    '--text-status-bar': '#FFFFFF',
    '--text-button-primary': '#FFFFFF', // White text on colored buttons
    '--text-list-item-selected': '#4E342E', // Dark text on amber selected
    '--text-placeholder': '#A1887F', // Light Brown
    '--text-highlighted': '#4E342E',
    '--text-search-highlighted': '#4E342E',
    '--text-ssml-editor-file-info': '#795548',
    '--text-ssml-instructions': '#4E342E',
    '--text-ssml-tag': '#E65100', // Dark Orange for tags
    '--text-ssml-attr-name': '#FF6F00', // Bright Orange
    '--text-ssml-attr-value': '#D84315', // Deep Orange
    '--text-ssml-comment': '#A1887F',

    '--border-primary': '#FFCC80', // Light Orange borders
    '--border-secondary': '#FFE0B2',
    '--border-input': '#FFB74D', // Medium Orange
    '--border-ssml-text-widget': '#FFB74D',
    '--border-ssml-instructions': '#FFCC80',
    '--border-ssml-preview-modal-content': '#FFCC80',

    '--accent-primary': '#FFB300', // Amber
    '--accent-secondary': '#FF7043', // Coral
    '--accent-secondary-hover': '#FF5722', // Darker Coral (Deep Orange)
    '--accent-playback': '#FFCA28', // Lighter Amber for playback
    '--accent-playback-hover': '#FFC107', // Amber (darker than playback)
    '--accent-highlight': '#FFECB3', // Pale Orange highlight
    '--accent-search-highlight': '#FFFF8D', // Light Yellow
    '--accent-volume-slider': '#FFB300',

    '--button-primary-bg': '#FFC107', // Amber buttons
    '--button-primary-hover-bg': '#FFA000', // Darker Amber
    '--button-primary-active-bg': '#E65100', // Active nav button matches status bar
    '--button-disabled-bg': '#FFE0B2',
    '--button-file-input-bg': '#FF9800', // Orange for file inputs
    '--button-file-input-hover-bg': '#FB8C00',
    
    '--button-accent-bg': '#FF7043', // Coral
    '--button-accent-hover-bg': '#FF5722', // Deep Orange
    '--button-warning-bg': '#F4511E', // Deep Orange/Red
    '--button-warning-hover-bg': '#E64A19',
    '--button-info-bg': '#FFB300', // Amber
    '--button-info-hover-bg': '#FFA000',
    
    '--shadow-light': 'rgba(0,0,0,0.04)',
    '--shadow-medium': 'rgba(0,0,0,0.08)',
    '--shadow-modal': 'rgba(0,0,0,0.15)',
    '--shadow-inset-active-nav': 'rgba(0,0,0,0.1)',

    '--icon-primary': '#FF7043', // Coral icons
    '--icon-ms-tts': '#FFB300', // Amber for MS TTS icon
    '--icon-selected-item': '#4E342E',

    '--status-processing-color': '#FFB300',
    '--status-ok-color': '#66BB6A', // Light Green
    '--status-flagged-color': '#FFA726', // Orange
    '--status-error-color': '#EF5350', // Red
    '--status-corrected-color': '#FF7043',
    '--status-no-text-color': '#A1887F',
  }
};