
export const vintagePaperTheme = {
  name: 'Vintage Paper',
  properties: {
    '--bg-primary': '#F5EFE6', // Aged Paper / Light Beige
    '--bg-secondary': '#FFFFFF', // White (for contrast where needed)
    '--bg-app-container': '#F5EFE6',
    '--bg-panel': '#FAF6F0', // Slightly Lighter Beige Panel
    '--bg-header': '#795548', // Medium Brown Header
    '--bg-status-bar': '#4E342E', // Dark Brown Status Bar
    '--bg-modal-backdrop': 'rgba(0, 0, 0, 0.5)',
    '--bg-modal-content': '#FAF6F0',
    '--bg-input': '#FAF6F0', // Input background same as panel
    '--bg-input-disabled': '#E0D8CD', // Muted beige
    '--bg-list-box': '#FAF6F0',
    '--bg-list-item-hover': '#EBE2D9', // Darker beige hover
    '--bg-list-item-selected': '#8D6E63', // <PERSON> selected
    '--bg-ssml-editor-view': '#F5EFE6',
    '--bg-ssml-instructions': '#EBE2D9',
    '--bg-ssml-preview-modal-content': '#F0EAE0',

    '--text-primary': '#4E342E', // Dark Brown
    '--text-secondary': '#6D4C41', // Medium-Dark Brown
    '--text-panel-heading': '#3E2723', // Darkest Brown for headings
    '--text-header': '#F5EFE6', // Light text on dark header
    '--text-status-bar': '#F5EFE6',
    '--text-button-primary': '#F5EFE6', // Light text on colored buttons
    '--text-list-item-selected': '#F5EFE6', // Light text on selected item
    '--text-placeholder': '#A1887F', // Muted Brown
    '--text-highlighted': '#FFFFFF', // White text on dark highlight
    '--text-search-highlighted': '#FFFFFF',
    '--text-ssml-editor-file-info': '#6D4C41',
    '--text-ssml-instructions': '#4E342E',
    '--text-ssml-tag': '#3E2723', // Darkest Brown for tags
    '--text-ssml-attr-name': '#BF360C', // Deep Orange/Rust
    '--text-ssml-attr-value': '#5D4037', // Darker Brown
    '--text-ssml-comment': '#A1887F',

    '--border-primary': '#D7CCC8', // Light Brown-Gray borders
    '--border-secondary': '#EFEBE9',
    '--border-input': '#BCAAA4', // Medium Brown-Gray
    '--border-ssml-text-widget': '#BCAAA4',
    '--border-ssml-instructions': '#D7CCC8',
    '--border-ssml-preview-modal-content': '#D7CCC8',

    '--accent-primary': '#8D6E63', // Light Brown
    '--accent-secondary': '#A1887F', // Muted Brown
    '--accent-secondary-hover': '#8D6E63', // Darker Muted Brown (Light Brown)
    '--accent-playback': '#BCAAA4', // Lighter Brown-Gray for playback
    '--accent-playback-hover': '#A1887F', // Darker Lighter Brown-Gray (Muted Brown)
    '--accent-highlight': '#795548', // Medium Brown highlight bg
    '--accent-search-highlight': '#BF360C', // Deep Orange/Rust search highlight bg
    '--accent-volume-slider': '#8D6E63',

    '--button-primary-bg': '#795548', // Medium Brown buttons
    '--button-primary-hover-bg': '#6D4C41',
    '--button-primary-active-bg': '#4E342E', // Active nav button matches status bar
    '--button-disabled-bg': '#D7CCC8',
    '--button-file-input-bg': '#8D6E63', // Light Brown for file inputs
    '--button-file-input-hover-bg': '#795548',
    
    '--button-accent-bg': '#A1887F', // Muted Brown
    '--button-accent-hover-bg': '#8D6E63',
    '--button-warning-bg': '#BF360C', // Deep Orange/Rust
    '--button-warning-hover-bg': '#A62D0A',
    '--button-info-bg': '#8D6E63', // Light Brown
    '--button-info-hover-bg': '#795548',
    
    '--shadow-light': 'rgba(0,0,0,0.05)',
    '--shadow-medium': 'rgba(0,0,0,0.1)',
    '--shadow-modal': 'rgba(0,0,0,0.2)',
    '--shadow-inset-active-nav': 'rgba(0,0,0,0.15)',

    '--icon-primary': '#A1887F', // Muted Brown icons
    '--icon-ms-tts': '#8D6E63', // Light Brown for MS TTS icon
    '--icon-selected-item': '#F5EFE6',

    '--status-processing-color': '#8D6E63',
    '--status-ok-color': '#689F38', // Olive Green
    '--status-flagged-color': '#FF8F00', // Amber
    '--status-error-color': '#C62828', // Dark Red
    '--status-corrected-color': '#A1887F',
    '--status-no-text-color': '#A1887F',
  }
};