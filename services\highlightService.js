
import * as dom from '../domElements.js';
import * as state from '../state.js';

export function updateWordHighlight() {
    const viewer = state.isDocEditing ? null : (dom.docHtmlViewer.style.display !== 'none' ? dom.docHtmlViewer : null);
    if (!viewer || !state.isPlaying || state.isPausedManually || !state.currentAudioFile || !dom.audioPlayer.duration) {
        clearHighlights(viewer);
        state.setHighlightedWordIndex(-1);
        return;
    }

    // Make sure viewer content is up-to-date if it's based on textarea (though it shouldn't be if !isDocEditing)
    // This basic implementation assumes viewer.innerText is stable during playback
    const plainTextContent = viewer.innerText || ""; 
    const words = plainTextContent.match(/\S+/g) || [];

    if (words.length === 0) {
        clearHighlights(viewer);
        state.setHighlightedWordIndex(-1);
        return;
    }
    
    // Calculate current word based on playback progress
    // This is a simplified approach. A more robust solution would use timing information if available (e.g., from SSML marks).
    const progressRatio = Math.min(1, dom.audioPlayer.currentTime / dom.audioPlayer.duration);
    let currentWordIdx = Math.floor(progressRatio * words.length);
    if (currentWordIdx >= words.length) currentWordIdx = words.length - 1; // Cap at last word
    
    if (currentWordIdx === state.highlightedWordIndex) return; // No change in highlighted word

    state.setHighlightedWordIndex(currentWordIdx);
    clearHighlights(viewer); // Clear previous highlights

    if (state.highlightedWordIndex === -1 || state.highlightedWordIndex >= words.length) return;

    // Find and highlight the new word. This is complex due to HTML structure.
    // This simplified version will try to find the Nth word in text nodes.
    const targetWord = words[state.highlightedWordIndex];
    let charCount = 0; // Tracks characters traversed in plainTextContent
    let wordFound = false;

    // TreeWalker to iterate over text nodes
    const walker = document.createTreeWalker(viewer, NodeFilter.SHOW_TEXT, null);
    let node;
    while(node = walker.nextNode()) {
        if (node.parentNode && (node.parentNode.tagName === 'SCRIPT' || node.parentNode.tagName === 'STYLE')) continue; // Skip script/style content
        
        const nodeText = node.nodeValue || "";
        let startIndexInNode = 0; // Where to start searching in the current text node
        
        while(startIndexInNode < nodeText.length) {
            // Calculate how many words are *before* the current point in this text node within the overall document
            const textBeforeInDoc = plainTextContent.substring(0, charCount + startIndexInNode);
            const wordsBefore = (textBeforeInDoc.match(/\S+/g) || []).length;

            // Search for the targetWord in the remainder of the current text node
            const searchSegment = nodeText.substring(startIndexInNode);
            const tempIndexInSegment = searchSegment.indexOf(targetWord);

            if (tempIndexInSegment !== -1) {
                const actualIndexInNode = startIndexInNode + tempIndexInSegment;
                // Check if this occurrence is the Nth word we are looking for globally
                 const textUpToThisWordOccurrence = plainTextContent.substring(0, charCount + actualIndexInNode);
                 const wordsUpToThisOccurrence = (textUpToThisWordOccurrence.match(/\S+/g) || []).length;

                if(wordsUpToThisOccurrence === state.highlightedWordIndex) {
                    // Found the correct word occurrence
                    const range = document.createRange();
                    range.setStart(node, actualIndexInNode);
                    range.setEnd(node, actualIndexInNode + targetWord.length);
                    
                    const highlightSpan = document.createElement('span');
                    highlightSpan.className = 'highlight';
                    
                    try {
                        range.surroundContents(highlightSpan);
                        wordFound = true;
                    } catch (e) { 
                        // console.warn("Error surrounding word:", e, "Node:", node, "Index:", actualIndexInNode, "Word:", targetWord);
                        // This can happen if the range is invalid (e.g., spans across elements in complex ways not handled here)
                    }
                    break; // Break from inner while loop
                }
                // If not the Nth word, continue searching from after this occurrence in the current node
                startIndexInNode = actualIndexInNode + targetWord.length;
            } else {
                // Word not found in the rest of this node
                break; // Break from inner while loop
            }
        }
        if (wordFound) break; // Break from outer while loop (TreeWalker)
        charCount += nodeText.length; // Add length of this node to overall character count
    }
}

export function clearHighlights(container) {
    if (!container) return;
    const highlights = Array.from(container.querySelectorAll('span.highlight'));
    highlights.forEach(span => {
        const parent = span.parentNode;
        if (parent) {
            // Replace span with its text content
            while (span.firstChild) {
                parent.insertBefore(span.firstChild, span);
            }
            parent.removeChild(span);
            parent.normalize(); // Merges adjacent text nodes
        }
    });
}
