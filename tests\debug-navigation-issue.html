<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Navigation Issue</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .console-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .iframe-container {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            margin: 10px 0;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🐛 Debug Navigation Issue</h1>
        <p>This page helps diagnose why tab navigation is not working in the main application.</p>
        
        <div class="test-result info">
            <h3>🔍 Diagnostic Tests</h3>
            <button onclick="checkMainApp()">Check Main App</button>
            <button onclick="checkConsoleErrors()">Check Console Errors</button>
            <button onclick="checkDOMElements()">Check DOM Elements</button>
            <button onclick="checkEventListeners()">Check Event Listeners</button>
            <button onclick="simulateClicks()">Simulate Tab Clicks</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="test-result info">
            <h3>📊 Console Output</h3>
            <div id="console-output" class="console-log">Waiting for diagnostic tests...</div>
        </div>
        
        <div class="test-result info">
            <h3>🖥️ Main Application (Embedded)</h3>
            <div class="iframe-container">
                <iframe id="main-app-iframe" src="http://localhost:8000"></iframe>
            </div>
        </div>
    </div>

    <script>
        const consoleOutput = document.getElementById('console-output');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            consoleOutput.textContent += logEntry;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }
        
        function clearLog() {
            consoleOutput.textContent = 'Log cleared.\n';
        }
        
        function checkMainApp() {
            log('Checking main application...', 'info');
            const iframe = document.getElementById('main-app-iframe');
            
            iframe.onload = function() {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    log('Main app loaded successfully', 'success');
                    
                    // Check if navigation buttons exist
                    const buttons = [
                        'audiobook-verification-btn',
                        'audiobook-text-editor-btn',
                        'ai-voice-creator-btn',
                        'quote-footnote-inserter-btn',
                        'pdf-to-text-converter-btn'
                    ];
                    
                    buttons.forEach(buttonId => {
                        const button = iframeDoc.getElementById(buttonId);
                        if (button) {
                            log(`✅ Button found: ${buttonId}`, 'success');
                        } else {
                            log(`❌ Button missing: ${buttonId}`, 'error');
                        }
                    });
                    
                    // Check if view containers exist
                    const views = [
                        'app-container',
                        'text-editor-view-container',
                        'ai-voice-creator-view-container',
                        'quote-footnote-inserter-view-container',
                        'pdf-to-text-converter-view-container'
                    ];
                    
                    views.forEach(viewId => {
                        const view = iframeDoc.getElementById(viewId);
                        if (view) {
                            log(`✅ View found: ${viewId}`, 'success');
                        } else {
                            log(`❌ View missing: ${viewId}`, 'error');
                        }
                    });
                    
                } catch (error) {
                    log(`Error accessing iframe content: ${error.message}`, 'error');
                }
            };
            
            iframe.onerror = function() {
                log('Failed to load main application', 'error');
            };
        }
        
        function checkConsoleErrors() {
            log('Checking for console errors...', 'info');
            
            // Override console methods to capture errors
            const originalError = console.error;
            const originalWarn = console.warn;
            
            console.error = function(...args) {
                log(`CONSOLE ERROR: ${args.join(' ')}`, 'error');
                originalError.apply(console, args);
            };
            
            console.warn = function(...args) {
                log(`CONSOLE WARN: ${args.join(' ')}`, 'warning');
                originalWarn.apply(console, args);
            };
            
            // Listen for unhandled errors
            window.addEventListener('error', function(event) {
                log(`UNHANDLED ERROR: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
            });
            
            log('Console error monitoring enabled', 'info');
        }
        
        function checkDOMElements() {
            log('Checking DOM elements in main app...', 'info');
            const iframe = document.getElementById('main-app-iframe');
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const iframeWindow = iframe.contentWindow;
                
                // Check if DOM elements module is loaded
                if (iframeWindow.dom) {
                    log('DOM elements module found', 'success');
                } else {
                    log('DOM elements module not found', 'error');
                }
                
                // Check specific elements
                const elementsToCheck = [
                    'audiobookVerificationBtn',
                    'audiobookTextEditorBtn',
                    'aiVoiceCreatorBtn',
                    'quoteFootnoteInserterBtn',
                    'pdfToTextConverterBtn'
                ];
                
                elementsToCheck.forEach(elementName => {
                    const element = iframeDoc.getElementById(elementName.replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, ''));
                    if (element) {
                        log(`✅ Element accessible: ${elementName}`, 'success');
                    } else {
                        log(`❌ Element not accessible: ${elementName}`, 'error');
                    }
                });
                
            } catch (error) {
                log(`Error checking DOM elements: ${error.message}`, 'error');
            }
        }
        
        function checkEventListeners() {
            log('Checking event listeners...', 'info');
            const iframe = document.getElementById('main-app-iframe');
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // Check if buttons have event listeners
                const buttons = [
                    'audiobook-verification-btn',
                    'audiobook-text-editor-btn',
                    'ai-voice-creator-btn',
                    'quote-footnote-inserter-btn',
                    'pdf-to-text-converter-btn'
                ];
                
                buttons.forEach(buttonId => {
                    const button = iframeDoc.getElementById(buttonId);
                    if (button) {
                        // Check if button has click listeners
                        const hasListeners = button.onclick !== null || 
                                           (button.addEventListener && button.getEventListeners);
                        log(`Button ${buttonId} has listeners: ${hasListeners}`, hasListeners ? 'success' : 'warning');
                    }
                });
                
            } catch (error) {
                log(`Error checking event listeners: ${error.message}`, 'error');
            }
        }
        
        function simulateClicks() {
            log('Simulating tab clicks...', 'info');
            const iframe = document.getElementById('main-app-iframe');
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                const buttons = [
                    'audiobook-text-editor-btn',
                    'ai-voice-creator-btn',
                    'quote-footnote-inserter-btn',
                    'pdf-to-text-converter-btn'
                ];
                
                buttons.forEach((buttonId, index) => {
                    setTimeout(() => {
                        const button = iframeDoc.getElementById(buttonId);
                        if (button) {
                            log(`Clicking button: ${buttonId}`, 'info');
                            button.click();
                        } else {
                            log(`Button not found: ${buttonId}`, 'error');
                        }
                    }, index * 1000);
                });
                
            } catch (error) {
                log(`Error simulating clicks: ${error.message}`, 'error');
            }
        }
        
        // Auto-start diagnostics
        setTimeout(() => {
            log('Starting automatic diagnostics...', 'info');
            checkConsoleErrors();
            setTimeout(checkMainApp, 1000);
        }, 500);
    </script>
</body>
</html>
