
:root {
  /* Default Theme (Light) - Aligned with original hardcoded values where possible */
  --bg-primary: #f4f7f9;
  --bg-secondary: #ffffff;
  --bg-app-container: #fff;
  --bg-panel: #fdfdfd;
  --bg-header: #34495e;
  --bg-status-bar: #2c3e50;
  --bg-modal-backdrop: rgba(0, 0, 0, 0.5);
  --bg-modal-content: #ffffff;
  --bg-input: #ffffff;
  --bg-input-disabled: #ecf0f1;
  --bg-list-box: #fff;
  --bg-list-item-hover: #e8f0f5;
  --bg-list-item-selected: #3498db;
  --bg-ssml-editor-view: #eef1f3;
  --bg-ssml-instructions: #eaf2f8;
  --bg-ssml-preview-modal-content: #eee;

  --text-primary: #333;
  --text-secondary: #555;
  --text-panel-heading: #2c3e50;
  --text-header: #ffffff;
  --text-status-bar: #ffffff;
  --text-button-primary: #ffffff;
  --text-list-item-selected: #ffffff;
  --text-placeholder: #7f8c8d;
  --text-highlighted: #333;
  --text-search-highlighted: #000000;
  --text-ssml-editor-file-info: #555;
  --text-ssml-instructions: #34495e;
  --text-ssml-tag: #2980b9;
  --text-ssml-attr-name: #c0392b;
  --text-ssml-attr-value: #27ae60;
  --text-ssml-comment: #7f8c8d;

  --border-primary: #dde4e9;
  --border-secondary: #ecf0f1;
  --border-input: #bdc3c7;
  --border-ssml-text-widget: #bdc3c7;
  --border-ssml-instructions: #d4e6f1;
  --border-ssml-preview-modal-content: #ccc;

  --accent-primary: #3498db; /* Main interactive color, progress bars */
  --accent-secondary: #1abc9c; /* E.g. Break tag buttons */
  --accent-secondary-hover: #16a085; /* Hover for accent-secondary */
  --accent-playback: #2ecc71; /* E.g. Playback buttons */
  --accent-playback-hover: #27ae60; /* Hover for accent-playback */
  --accent-highlight: #f1c40f; /* Word highlight */
  --accent-search-highlight: yellow; /* SSML search highlight */
  --accent-volume-slider: #3498db;

  --button-primary-bg: #5dade2;
  --button-primary-hover-bg: #4a90e2;
  --button-primary-active-bg: #2980b9; /* For active nav button */
  --button-disabled-bg: #bdc3c7;
  --button-file-input-bg: #3498db;
  --button-file-input-hover-bg: #2980b9;
  
  --button-accent-bg: #27ae60; /* Verification start, SSML Save */
  --button-accent-hover-bg: #2ecc71;
  --button-warning-bg: #e67e22;
  --button-warning-hover-bg: #f39c12;
  --button-info-bg: #3498db;
  --button-info-hover-bg: #5dade2;
  
  --shadow-light: rgba(0,0,0,0.05);
  --shadow-medium: rgba(0,0,0,0.1);
  --shadow-modal: rgba(0,0,0,0.2);
  --shadow-inset-active-nav: rgba(0,0,0,0.2);

  --icon-primary: #1abc9c; /* Playlist Google TTS icon */
  --icon-ms-tts: #0078D4; /* Playlist MS TTS icon */
  --icon-selected-item: #ffffff;

  /* Status specific colors for verification */
  --status-processing-color: #3498db;
  --status-ok-color: #2ecc71;
  --status-flagged-color: #e67e22;
  --status-error-color: #e74c3c;
  --status-corrected-color: #9b59b6; /* Retained if specifically used, general corrected is green */
  --status-no-text-color: #7f8c8d;
}

/* Header theme selector styling */
.theme-selector-container {
    margin-left: auto; /* Pushes it to the right */
}

#theme-select {
    padding: 6px 10px;
    border-radius: 4px;
    border: 1px solid var(--border-input);
    background-color: var(--bg-input);
    color: var(--text-primary);
    font-size: 0.9em;
    cursor: pointer;
    min-width: 150px; /* Ensure it's not too small */
}

#theme-select:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 1px;
}