import * as state from '../state.js';

let availableThemes = {};

// Define a fallback theme in case the import fails, ensuring the app doesn't crash.
const fallbackTheme = {
    'default': {
        name: 'Default Light (Fallback)',
        properties: {
            '--bg-primary': '#f4f7f9',
            '--bg-secondary': '#ffffff',
            '--text-primary': '#333',
            '--bg-header': '#34495e',
            '--text-header': '#ffffff',
            '--button-primary-bg': '#5dade2',
            '--text-button-primary': '#ffffff',
            '--border-primary': '#dde4e9',
            '--bg-panel': '#fdfdfd',
        }
    }
};

export async function initThemes() {
    try {
        // Import individual theme files instead of the problematic themes/index.js
        const [defaultTheme, forestTheme, oceanTheme, crimsonTheme, cyberTheme, arcticTheme, sunriseTheme, vintageTheme] = await Promise.all([
            import('../themes/defaultTheme.js').catch(() => null),
            import('../themes/forestWhisperTheme.js').catch(() => null),
            import('../themes/oceanDeepTheme.js').catch(() => null),
            import('../themes/crimsonNightTheme.js').catch(() => null),
            import('../themes/cyberPulseTheme.js').catch(() => null),
            import('../themes/arcticFrostTheme.js').catch(() => null),
            import('../themes/sunriseGlowTheme.js').catch(() => null),
            import('../themes/vintagePaperTheme.js').catch(() => null)
        ]);

        // Build themes object from individual imports
        availableThemes = {};
        if (defaultTheme?.defaultTheme) availableThemes.default = defaultTheme.defaultTheme;
        if (forestTheme?.forestWhisperTheme) availableThemes.forestWhisper = forestTheme.forestWhisperTheme;
        if (oceanTheme?.oceanDeepTheme) availableThemes.oceanDeep = oceanTheme.oceanDeepTheme;
        if (crimsonTheme?.crimsonNightTheme) availableThemes.crimsonNight = crimsonTheme.crimsonNightTheme;
        if (cyberTheme?.cyberPulseTheme) availableThemes.cyberPulse = cyberTheme.cyberPulseTheme;
        if (arcticTheme?.arcticFrostTheme) availableThemes.arcticFrost = arcticTheme.arcticFrostTheme;
        if (sunriseTheme?.sunriseGlowTheme) availableThemes.sunriseGlow = sunriseTheme.sunriseGlowTheme;
        if (vintageTheme?.vintagePaperTheme) availableThemes.vintagePaper = vintageTheme.vintagePaperTheme;

        console.log("Successfully loaded individual theme files");
    } catch (error) {
        console.error("Could not load theme files. Using fallback theme.", error);
        availableThemes = fallbackTheme;
    }
    // Ensure there's always at least a default theme
    if (!availableThemes['default']) {
        availableThemes = { ...availableThemes, ...fallbackTheme };
    }
    state.setCurrentThemeName('default');
}

export function applyTheme(themeName) {
    const theme = availableThemes[themeName];
    if (!theme) {
        console.warn(`Theme "${themeName}" not found. Applying default theme.`);
        applyTheme('default'); // Fallback to default
        return;
    }

    if (!theme.properties || typeof theme.properties !== 'object') {
        console.error(`Theme "${themeName}" has invalid properties. Cannot apply theme.`);
        return;
    }

    const root = document.documentElement;
    
    Object.keys(theme.properties).forEach(property => {
        root.style.setProperty(property, theme.properties[property]);
    });
    
    console.log(`[Theme Service] Applied theme: ${theme.name} (key: ${themeName}).`);
    state.setCurrentThemeName(themeName);
}

export function getAvailableThemes() {
    return availableThemes;
}

export function getCurrentTheme() {
    return availableThemes[state.currentThemeName];
}