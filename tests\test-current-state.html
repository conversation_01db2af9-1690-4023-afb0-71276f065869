<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Current State</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Current State Test</h1>
    
    <div class="test-result info">
        <h3>🔍 Testing Navigation Fix</h3>
        <p>This page will help verify if the audiobook text editor navigation is working.</p>
    </div>
    
    <div class="test-result info">
        <h3>📋 What to Test</h3>
        <ol>
            <li>Open the main application below</li>
            <li>Try clicking the "Audiobook Text Editor" button</li>
            <li>Check if the view switches to show the SSML editor</li>
            <li>Try clicking between different tabs</li>
            <li>Check browser console (F12) for debug messages</li>
        </ol>
    </div>
    
    <div class="test-result info">
        <h3>🎯 Expected Results</h3>
        <ul>
            <li>✅ Navigation buttons should respond to clicks</li>
            <li>✅ Views should switch properly (hide/show)</li>
            <li>✅ Console should show debug messages like "Text Editor button clicked!"</li>
            <li>✅ No JavaScript errors in console</li>
            <li>✅ SSML text area should be visible when text editor tab is active</li>
        </ul>
    </div>
    
    <button onclick="openMainApp()">🚀 Open Main App in New Tab</button>
    <button onclick="openDebugApp()">🔍 Open Debug Version</button>
    <button onclick="refreshIframe()">🔄 Refresh Embedded App</button>
    
    <h3>📱 Embedded Application</h3>
    <iframe id="app-iframe" src="http://localhost:8000/?v=3"></iframe>
    
    <div class="test-result info">
        <h3>🐛 Troubleshooting</h3>
        <p>If navigation still doesn't work:</p>
        <ul>
            <li>Check browser console for JavaScript errors</li>
            <li>Verify that DOM elements are being found correctly</li>
            <li>Look for debug messages from domElements.js</li>
            <li>Check if event listeners are being attached</li>
        </ul>
    </div>
    
    <script>
        function openMainApp() {
            window.open('http://localhost:8000/?v=' + Date.now(), '_blank');
        }
        
        function openDebugApp() {
            window.open('http://localhost:8000/tests/debug-navigation.html', '_blank');
        }
        
        function refreshIframe() {
            const iframe = document.getElementById('app-iframe');
            iframe.src = 'http://localhost:8000/?v=' + Date.now();
        }
        
        // Auto-refresh iframe every 30 seconds for testing
        setInterval(() => {
            console.log('Auto-refreshing iframe for testing...');
            refreshIframe();
        }, 30000);
    </script>
</body>
</html>
