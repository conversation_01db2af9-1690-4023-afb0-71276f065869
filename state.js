
// --- Global State ---
export let musicFiles = [];
export let docFiles = [];
export let currentTrackId = null;
export let currentDocId = null;
export let isPlaying = false;
export let isPausedManually = false;
export let currentAudioFile = null;
export let currentDocumentFile = null;
export let isDocEditing = false;

// Google TTS State
export let fetchedGTTSVoices = []; // Renamed from fetchedTTSVoices
export let selectedGTTSVoiceName = ''; // Renamed, will be initialized with DEFAULT_G_TTS_VOICE_NAME
export let isGTTSreprocessing = false; // Renamed from isTTSReprocessing
export let areGVoicesLoading = false; // Renamed from areVoicesLoading

// Microsoft TTS State
export let fetchedMsTTSVoices = [];
export let selectedMsTTSVoiceName = ''; // Will be initialized with DEFAULT_MS_TTS_VOICE_NAME
export let isMsTTSReprocessing = false;
export let areMsVoicesLoading = false;

export let highlightedWordIndex = -1;

// Theme State
export let currentThemeName = 'default'; // Default theme

// SSML Editor Specific State
export let ssmlEditorFile = null; // Stores the File object loaded into SSML editor
export let ssmlEditorRawContent = ""; // Stores the raw text content of the ssmlEditorFile

// AI Verification Feature State
export let isVerifying = false;
// Structure for verificationResults items:
// {
//   originalAudioFile: { name: string, id: string },
//   status: string ("Processing...", "Checked - OK", "Flagged - Needs Reprocessing", "Corrected & Reprocessed", "Error...", "Replaced with Correction"),
//   whisperTranscriptPreview?: string,
//   aiAnalysis?: { identifiedIssues: number, ssmlText: string },
//   reprocessedAudio?: { id: string, file: File, name: string, duration: number, objectURL: string, isSynthesized: boolean, ttsProvider: string, voiceName: string },
//   replaced?: boolean // True if original has been replaced by this verified version
// }
export let verificationResults = []; 
export let currentVerificationFileIndex = -1; // Index of file being verified from musicFiles
export let verificationPreviewAudioURL = null; // Object URL for previewing reprocessed audio


// Functions to update state, makes it easier to track changes if needed later
export function setMusicFiles(newFiles) { musicFiles = newFiles; }
export function setDocFiles(newFiles) { docFiles = newFiles; }
export function setCurrentTrackId(id) { currentTrackId = id; }
export function setCurrentDocId(id) { currentDocId = id; }
export function setIsPlaying(playing) { isPlaying = playing; }
export function setIsPausedManually(paused) { isPausedManually = paused; }
export function setCurrentAudioFile(file) { currentAudioFile = file; }
export function setCurrentDocumentFile(file) { currentDocumentFile = file; }
export function setIsDocEditing(editing) { isDocEditing = editing; }

export function setFetchedGTTSVoices(voices) { fetchedGTTSVoices = voices; }
export function setSelectedGTTSVoiceName(name) { selectedGTTSVoiceName = name; }
export function setIsGTTSreprocessing(reprocessing) { isGTTSreprocessing = reprocessing; }
export function setAreGVoicesLoading(loading) { areGVoicesLoading = loading; }

export function setFetchedMsTTSVoices(voices) { fetchedMsTTSVoices = voices; }
export function setSelectedMsTTSVoiceName(name) { selectedMsTTSVoiceName = name; }
export function setIsMsTTSReprocessing(reprocessing) { isMsTTSReprocessing = reprocessing; }
export function setAreMsVoicesLoading(loading) { areMsVoicesLoading = loading; }

export function setHighlightedWordIndex(index) { highlightedWordIndex = index; }

export function setCurrentThemeName(name) { currentThemeName = name; }

// Setters for SSML Editor State
export function setSsmlEditorFile(file) { ssmlEditorFile = file; }
export function setSsmlEditorRawContent(content) { ssmlEditorRawContent = content; }

// Setters for AI Verification State
export function setIsVerifying(verifying) { isVerifying = verifying; }
export function setVerificationResults(results) { verificationResults = results; }
export function setCurrentVerificationFileIndex(index) { currentVerificationFileIndex = index; }
export function setVerificationPreviewAudioURL(url) { verificationPreviewAudioURL = url;}
