#!/usr/bin/env node

/**
 * Mistral Auto-Start Server
 * Provides API endpoints for auto-starting Mistral servers
 */

import express from 'express';
import { spawn, exec } from 'child_process';
import cors from 'cors';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.')); // Serve static files

// Store running processes
const runningProcesses = new Map();

/**
 * Check if Docker is available
 */
app.post('/api/docker/check', (req, res) => {
    exec('docker --version', (error, stdout, stderr) => {
        if (error) {
            res.status(404).json({
                available: false,
                error: error.message
            });
        } else {
            res.json({
                available: true,
                version: stdout.trim()
            });
        }
    });
});

/**
 * Check system requirements for real Mistral deployment
 */
app.get('/api/system/requirements', (req, res) => {
    const requirements = {
        python: false,
        vllm: false,
        torch: false,
        memory: 0,
        disk: 0,
        huggingface: false,
        recommendations: []
    };

    // Check Python
    exec('python --version', (pythonError, pythonStdout) => {
        requirements.python = !pythonError;
        if (requirements.python) {
            requirements.pythonVersion = pythonStdout.trim();
        }

        // Check vLLM installation
        exec('python -c "import vllm; print(vllm.__version__)"', (vllmError, vllmStdout) => {
            requirements.vllm = !vllmError;
            if (requirements.vllm) {
                requirements.vllmVersion = vllmStdout.trim();
            }

            // Check PyTorch installation
            exec('python -c "import torch; print(torch.__version__)"', (torchError, torchStdout) => {
                requirements.torch = !torchError;
                if (requirements.torch) {
                    requirements.torchVersion = torchStdout.trim();
                }

            // Check available memory (approximate)
            exec('docker system df', (memError, memStdout) => {
                if (!memError) {
                    // Parse Docker system info for available space
                    const lines = memStdout.split('\n');
                    const imageLine = lines.find(line => line.includes('Images'));
                    if (imageLine) {
                        const match = imageLine.match(/(\d+\.?\d*)(GB|MB)/);
                        if (match) {
                            requirements.disk = match[1] + match[2];
                        }
                    }
                }

                // Generate recommendations
                if (!requirements.python) {
                    requirements.recommendations.push({
                        type: 'critical',
                        message: 'Install Python 3.8 or higher',
                        action: 'Download from https://python.org/downloads/'
                    });
                }

                if (!requirements.vllm) {
                    requirements.recommendations.push({
                        type: 'warning',
                        message: 'vLLM not installed',
                        action: 'Run: pip install vllm[vision] torch torchvision'
                    });
                }

                if (!requirements.torch) {
                    requirements.recommendations.push({
                        type: 'warning',
                        message: 'PyTorch not installed',
                        action: 'Run: pip install torch torchvision'
                    });
                }

                requirements.recommendations.push({
                    type: 'info',
                    message: 'Pixtral-12B requires ~24GB disk space and 8GB+ RAM',
                    action: 'Ensure sufficient system resources before deployment'
                });

                requirements.recommendations.push({
                    type: 'info',
                    message: 'HuggingFace token required for model access',
                    action: 'Get token from https://huggingface.co/settings/tokens and accept license at https://huggingface.co/mistralai/Pixtral-12B-2409'
                });

                res.json(requirements);
            });
        });
    });

/**
 * Setup HuggingFace authentication
 */
app.post('/api/huggingface/setup', (req, res) => {
    const { token } = req.body;

    if (!token) {
        return res.status(400).json({
            success: false,
            error: 'HuggingFace token is required'
        });
    }

    // Set environment variable for this session
    process.env.HUGGING_FACE_HUB_TOKEN = token;
    process.env.HF_TOKEN = token;

    // Try to authenticate with HuggingFace CLI
    exec(`echo "${token}" | huggingface-cli login --token`, (error, stdout, stderr) => {
        if (error) {
            res.status(500).json({
                success: false,
                error: 'Failed to authenticate with HuggingFace',
                details: error.message
            });
        } else {
            res.json({
                success: true,
                message: 'HuggingFace authentication successful',
                details: stdout
            });
        }
    });
});

/**
 * Start Mistral Docker container
 */
app.post('/api/docker/start-mistral', (req, res) => {
    const { port = '8000', model = 'mistral-large-latest', hfToken } = req.body;
    
    // Check if container already exists
    exec('docker ps -a --filter name=mistral-server --format "{{.Names}}"', (error, stdout) => {
        if (stdout.includes('mistral-server')) {
            // Container exists, try to start it
            exec('docker start mistral-server', (startError) => {
                if (startError) {
                    // Remove old container and create new one
                    exec('docker rm -f mistral-server', () => {
                        createNewContainer(port, model, res);
                    });
                } else {
                    res.json({ success: true, message: 'Existing container started' });
                }
            });
        } else {
            // Create new container
            createNewContainer(port, model, res, hfToken);
        }
    });
});

function createNewContainer(port, model, res, hfToken) {
    // Use vLLM with Pixtral-12B for vision capabilities
    const visionModel = 'mistralai/Pixtral-12B-2409'; // Mistral's vision model
    const token = hfToken || process.env.HUGGING_FACE_HUB_TOKEN || process.env.HF_TOKEN;

    // Check if NVIDIA runtime is available for GPU support
    exec('docker info | grep nvidia', (nvidiaCheck) => {
        const hasNvidia = !nvidiaCheck;

        let dockerCmd;
        const envVars = token ? `--env "HUGGING_FACE_HUB_TOKEN=${token}"` : '';

        if (hasNvidia) {
            // GPU-enabled command with NVIDIA runtime
            dockerCmd = `docker run -d --name mistral-server --runtime nvidia --gpus all ` +
                       `-v ~/.cache/huggingface:/root/.cache/huggingface ` +
                       `${envVars} ` +
                       `-p ${port}:8000 --ipc=host ` +
                       `vllm/vllm-openai:latest ` +
                       `--model ${visionModel} ` +
                       `--tokenizer_mode mistral ` +
                       `--load_format mistral ` +
                       `--config_format mistral ` +
                       `--limit_mm_per_prompt image=4 ` +
                       `--max_model_len 16384`;
        } else {
            // CPU-only fallback (slower but works without GPU)
            dockerCmd = `docker run -d --name mistral-server ` +
                       `-v ~/.cache/huggingface:/root/.cache/huggingface ` +
                       `${envVars} ` +
                       `-p ${port}:8000 --ipc=host ` +
                       `vllm/vllm-openai:latest ` +
                       `--model ${visionModel} ` +
                       `--tokenizer_mode mistral ` +
                       `--load_format mistral ` +
                       `--config_format mistral ` +
                       `--limit_mm_per_prompt image=4 ` +
                       `--max_model_len 8192 ` +
                       `--enforce_eager`;
        }

        console.log(`Starting Mistral container with command: ${dockerCmd}`);

        exec(dockerCmd, (error, stdout, stderr) => {
            if (error) {
                res.status(500).json({
                    success: false,
                    error: error.message,
                    stderr: stderr,
                    suggestion: hasNvidia ?
                        'GPU runtime failed. Try installing NVIDIA Docker runtime or use CPU mode.' :
                        'CPU mode failed. Ensure Docker has sufficient memory (8GB+ recommended).'
                });
            } else {
                res.json({
                    success: true,
                    containerId: stdout.trim(),
                    message: `Mistral Pixtral-12B container started successfully (${hasNvidia ? 'GPU' : 'CPU'} mode)`,
                    model: visionModel,
                    runtime: hasNvidia ? 'GPU' : 'CPU'
                });
            }
        });
    });
}

/**
 * Start offline vLLM Mistral server
 */
app.post('/api/mistral/start-offline', (req, res) => {
    const { port = '8000', hfToken } = req.body;

    // Set environment variables
    if (hfToken) {
        process.env.HF_TOKEN = hfToken;
        process.env.HUGGING_FACE_HUB_TOKEN = hfToken;
    }

    // Check if Python and vLLM are available
    exec('python --version', (pythonError, pythonStdout) => {
        if (pythonError) {
            return res.status(500).json({
                success: false,
                error: 'Python not found. Please install Python 3.8+ first.',
                suggestion: 'Download Python from https://python.org/downloads/'
            });
        }

        // Check if vLLM is installed
        exec('python -c "import vllm"', (vllmError) => {
            if (vllmError) {
                return res.status(500).json({
                    success: false,
                    error: 'vLLM not installed. Installing now...',
                    suggestion: 'Installing vLLM package. This may take several minutes.',
                    needsInstall: true
                });
            }

            // Start vLLM server
            startOfflineVLLM(port, res);
        });
    });
});

/**
 * Install vLLM package
 */
app.post('/api/mistral/install-vllm', (req, res) => {
    const { hfToken } = req.body;

    if (hfToken) {
        process.env.HF_TOKEN = hfToken;
        process.env.HUGGING_FACE_HUB_TOKEN = hfToken;
    }

    console.log("📦 Installing vLLM package...");

    // Install vLLM with vision support
    const installCmd = 'pip install vllm[vision] torch torchvision --upgrade';

    const installProcess = spawn('pip', ['install', 'vllm[vision]', 'torch', 'torchvision', '--upgrade'], {
        stdio: 'pipe',
        shell: true
    });

    let installOutput = '';

    installProcess.stdout.on('data', (data) => {
        installOutput += data.toString();
    });

    installProcess.stderr.on('data', (data) => {
        installOutput += data.toString();
    });

    installProcess.on('close', (code) => {
        if (code === 0) {
            res.json({
                success: true,
                message: 'vLLM installed successfully',
                output: installOutput
            });
        } else {
            res.status(500).json({
                success: false,
                error: 'vLLM installation failed',
                output: installOutput
            });
        }
    });

    // Store the process for potential cancellation
    runningProcesses.set('vllm-install', installProcess);
});

/**
 * Start mock Mistral server
 */
app.post('/api/mistral/start-mock', (req, res) => {
    const { port = '8000' } = req.body;

    // Check if port is already in use
    const mockServer = createMockServer(parseInt(port));

    mockServer.listen(parseInt(port), (err) => {
        if (err) {
            res.status(500).json({
                success: false,
                error: `Port ${port} is already in use or unavailable`
            });
        } else {
            runningProcesses.set(`mock-${port}`, mockServer);
            res.json({
                success: true,
                message: `Mock Mistral server started on port ${port}`,
                type: 'mock'
            });
        }
    });
});

/**
 * Create a mock Mistral server
 */
function createMockServer(port) {
    const mockApp = express();
    mockApp.use(cors());
    mockApp.use(express.json({ limit: '50mb' }));
    
    // Models endpoint
    mockApp.get('/v1/models', (req, res) => {
        res.json({
            object: "list",
            data: [
                {
                    id: "mistral-large-latest",
                    object: "model",
                    created: Date.now(),
                    owned_by: "mistral-mock"
                },
                {
                    id: "mistral-medium-latest",
                    object: "model",
                    created: Date.now(),
                    owned_by: "mistral-mock"
                },
                {
                    id: "mistral-small-latest",
                    object: "model",
                    created: Date.now(),
                    owned_by: "mistral-mock"
                }
            ]
        });
    });
    
    // Chat completions endpoint (enhanced mock vision processing)
    mockApp.post('/v1/chat/completions', (req, res) => {
        const { messages, model } = req.body;

        // Simulate processing delay
        setTimeout(() => {
            // Try to extract meaningful content from the request
            let extractedText = generateSmartMockText(messages);

            res.json({
                id: `chatcmpl-${Date.now()}`,
                object: "chat.completion",
                created: Math.floor(Date.now() / 1000),
                model: model || "mistral-large-latest",
                choices: [
                    {
                        index: 0,
                        message: {
                            role: "assistant",
                            content: extractedText
                        },
                        finish_reason: "stop"
                    }
                ],
                usage: {
                    prompt_tokens: 100,
                    completion_tokens: extractedText.length / 4,
                    total_tokens: 150 + (extractedText.length / 4)
                }
            });
        }, 500 + Math.random() * 1500); // 0.5-2 second delay
    });

    // Helper function to generate smarter mock text
    function generateSmartMockText(messages) {
        // Look for page information in the messages
        let pageInfo = "";
        let pageNumber = 1;

        if (messages && messages.length > 0) {
            const lastMessage = messages[messages.length - 1];
            if (lastMessage.content) {
                // Try to extract page number from the prompt
                const pageMatch = lastMessage.content.match(/page\s*(\d+)/i);
                if (pageMatch) {
                    pageNumber = parseInt(pageMatch[1]);
                    pageInfo = `Page ${pageNumber}`;
                }
            }
        }

        // Generate different content based on page number
        const mockContent = generatePageContent(pageNumber);

        return `${pageInfo ? pageInfo + '\n\n' : ''}${mockContent}\n\n[MOCK EXTRACTION] This is simulated text extraction. For real OCR, install actual Mistral AI with vision capabilities.`;
    }

    function generatePageContent(pageNumber) {
        const contentTemplates = [
            // Page 1 - Title page style
            "ST. ANTHONY\nTHE WONDER WORKER\n\nA Biography\n\nBy [Author Name]\n\nPublished by [Publisher]\n[Year]",

            // Page 2 - Table of contents style
            "TABLE OF CONTENTS\n\nChapter 1: Early Life ........................... 3\nChapter 2: Religious Calling ................... 15\nChapter 3: Miracles and Wonders ................ 28\nChapter 4: Legacy and Canonization ............. 45\nAppendix: Prayers and Devotions ................ 62",

            // Page 3+ - Chapter content style
            "Chapter " + Math.ceil(pageNumber / 10) + "\n\nSaint Anthony of Padua, also known as Anthony of Lisbon, was a Portuguese Catholic friar and mystic. Born Fernando Martins de Bulhões in 1195 in Lisbon, he would become one of the most beloved saints in the Catholic tradition.\n\nHis early life was marked by a deep devotion to learning and prayer. As a young man, he joined the Augustinian order and spent years studying theology and scripture. His intellectual gifts were evident from an early age, and he quickly gained recognition for his scholarly abilities.\n\nThe turning point in his life came when he encountered Franciscan friars who had been martyred in Morocco. Inspired by their sacrifice, he felt called to join the Franciscan order and dedicate his life to preaching and missionary work.\n\nAnthony's reputation as a powerful preacher spread throughout Italy and France. His sermons were said to be so moving that even fish would gather to listen when he preached by the water. This miracle, among many others, contributed to his reputation as a wonder worker.\n\nHis devotion to the poor and his ability to find lost objects made him particularly beloved by common people. Even today, millions of Catholics pray to Saint Anthony when they have lost something, earning him the title 'Finder of Lost Things.'\n\nThe saint's life was relatively short - he died at the age of 35 in 1231. However, his impact on the Church and on popular devotion was immense. He was canonized just one year after his death, making him one of the fastest canonizations in Church history."
        ];

        if (pageNumber === 1) {
            return contentTemplates[0];
        } else if (pageNumber === 2) {
            return contentTemplates[1];
        } else {
            // For other pages, use the chapter content with some variation
            let baseContent = contentTemplates[2];

            // Add some page-specific variation
            if (pageNumber % 3 === 0) {
                baseContent += "\n\nMany pilgrims traveled great distances to hear Anthony preach. His words had the power to convert even the most hardened hearts, and numerous miracles were attributed to his intercession.";
            } else if (pageNumber % 3 === 1) {
                baseContent += "\n\nThe Franciscan order provided Anthony with the perfect vehicle for his ministry. He embraced the ideals of poverty, humility, and service to others that characterized the followers of Saint Francis.";
            } else {
                baseContent += "\n\nAnthony's theological knowledge was vast, but he never let learning become an end in itself. Instead, he used his education to serve God and help others grow in faith and understanding.";
            }

            return baseContent;
        }
    }
    
    return mockApp;
}

/**
 * Start offline vLLM server
 */
function startOfflineVLLM(port, res) {
    const model = 'mistralai/Pixtral-12B-2409';

    // Create Python script for vLLM server
    const vllmScript = `
import os
from vllm import LLM
from vllm.sampling_params import SamplingParams
import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import base64
import io
from PIL import Image
import json

# Initialize FastAPI app
app = FastAPI(title="Mistral Offline Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize vLLM model
print("Loading Mistral Pixtral-12B model...")
llm = LLM(
    model="${model}",
    tokenizer_mode="mistral",
    load_format="mistral",
    config_format="mistral",
    limit_mm_per_prompt={"image": 4},
    max_model_len=16384
)
print("Model loaded successfully!")

# Pydantic models for API
class ChatMessage(BaseModel):
    role: str
    content: Any

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    max_tokens: Optional[int] = 4000
    temperature: Optional[float] = 0.1

class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str = "mistral-offline"

@app.get("/v1/models")
async def list_models():
    return {
        "object": "list",
        "data": [
            ModelInfo(
                id="${model}",
                created=1699000000,
                owned_by="mistral-offline"
            )
        ]
    }

@app.post("/v1/chat/completions")
async def chat_completions(request: ChatCompletionRequest):
    try:
        # Convert messages to vLLM format
        messages = []
        for msg in request.messages:
            if isinstance(msg.content, list):
                # Handle multimodal content
                content_parts = []
                for part in msg.content:
                    if part.get("type") == "text":
                        content_parts.append({"type": "text", "text": part["text"]})
                    elif part.get("type") == "image_url":
                        # Handle base64 images
                        image_url = part["image_url"]["url"]
                        if image_url.startswith("data:image"):
                            # Extract base64 data
                            base64_data = image_url.split(",")[1]
                            content_parts.append({
                                "type": "image_url",
                                "image_url": {"url": image_url}
                            })
                        else:
                            content_parts.append({
                                "type": "image_url",
                                "image_url": {"url": image_url}
                            })
                messages.append({"role": msg.role, "content": content_parts})
            else:
                messages.append({"role": msg.role, "content": msg.content})

        # Set up sampling parameters
        sampling_params = SamplingParams(
            max_tokens=request.max_tokens,
            temperature=request.temperature
        )

        # Generate response
        outputs = llm.chat(messages=messages, sampling_params=sampling_params)

        if outputs and len(outputs) > 0:
            response_text = outputs[0].outputs[0].text

            return {
                "id": f"chatcmpl-{hash(str(messages))}",
                "object": "chat.completion",
                "created": 1699000000,
                "model": request.model,
                "choices": [
                    {
                        "index": 0,
                        "message": {
                            "role": "assistant",
                            "content": response_text
                        },
                        "finish_reason": "stop"
                    }
                ],
                "usage": {
                    "prompt_tokens": 100,
                    "completion_tokens": len(response_text.split()),
                    "total_tokens": 100 + len(response_text.split())
                }
            }
        else:
            raise HTTPException(status_code=500, detail="No response generated")

    except Exception as e:
        print(f"Error in chat completion: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    return {"status": "ok", "model": "${model}"}

if __name__ == "__main__":
    print(f"Starting Mistral server on port ${port}")
    uvicorn.run(app, host="0.0.0.0", port=${port})
`;

    // Write the Python script to a temporary file
    const scriptPath = './mistral_offline_server.py';

    try {
        fs.writeFileSync(scriptPath, vllmScript);

        // Start the Python server
        const serverProcess = spawn('python', [scriptPath], {
            stdio: 'pipe',
            shell: true,
            env: { ...process.env }
        });

        let serverOutput = '';
        let serverReady = false;

        serverProcess.stdout.on('data', (data) => {
            const output = data.toString();
            serverOutput += output;
            console.log('vLLM Server:', output);

            // Check if server is ready
            if (output.includes('Uvicorn running') || output.includes('Application startup complete')) {
                serverReady = true;
            }
        });

        serverProcess.stderr.on('data', (data) => {
            const output = data.toString();
            serverOutput += output;
            console.log('vLLM Server Error:', output);
        });

        serverProcess.on('close', (code) => {
            console.log(`vLLM server process exited with code ${code}`);
            runningProcesses.delete('vllm-server');
        });

        // Store the process
        runningProcesses.set('vllm-server', serverProcess);

        // Wait a moment for initial startup
        setTimeout(() => {
            res.json({
                success: true,
                message: 'Offline vLLM server starting...',
                type: 'offline',
                model: model,
                port: port,
                note: 'Server is initializing. This may take several minutes for first-time model download.'
            });
        }, 1000);

    } catch (error) {
        res.status(500).json({
            success: false,
            error: `Failed to start offline server: ${error.message}`
        });
    }
}

/**
 * Stop running servers
 */
app.post('/api/mistral/stop', (req, res) => {
    const { type, port } = req.body;
    
    if (type === 'docker') {
        exec('docker stop mistral-server', (error) => {
            if (error) {
                res.status(500).json({ success: false, error: error.message });
            } else {
                res.json({ success: true, message: 'Docker container stopped' });
            }
        });
    } else if (type === 'mock') {
        const server = runningProcesses.get(`mock-${port}`);
        if (server) {
            server.close();
            runningProcesses.delete(`mock-${port}`);
            res.json({ success: true, message: 'Mock server stopped' });
        } else {
            res.status(404).json({ success: false, error: 'Mock server not found' });
        }
    } else {
        res.status(400).json({ success: false, error: 'Invalid server type' });
    }
});

/**
 * Get server status
 */
app.get('/api/mistral/status', (req, res) => {
    const status = {
        docker: false,
        mock: Array.from(runningProcesses.keys()).filter(k => k.startsWith('mock-')),
        timestamp: new Date().toISOString()
    };
    
    // Check Docker status
    exec('docker ps --filter name=mistral-server --format "{{.Names}}"', (error, stdout) => {
        status.docker = stdout.includes('mistral-server');
        res.json(status);
    });
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        service: 'mistral-auto-server',
        timestamp: new Date().toISOString()
    });
});

// Start the server
app.listen(PORT, () => {
    console.log(`🚀 Mistral Auto-Start Server running on port ${PORT}`);
    console.log(`📋 Available endpoints:`);
    console.log(`   POST /api/docker/check - Check Docker availability`);
    console.log(`   POST /api/docker/start-mistral - Start Mistral Docker container`);
    console.log(`   POST /api/mistral/start-mock - Start mock Mistral server`);
    console.log(`   POST /api/mistral/stop - Stop running servers`);
    console.log(`   GET  /api/mistral/status - Get server status`);
    console.log(`   GET  /health - Health check`);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down Mistral Auto-Start Server...');
    
    // Stop all mock servers
    runningProcesses.forEach((server, key) => {
        if (key.startsWith('mock-')) {
            server.close();
        }
    });
    
    process.exit(0);
});

// Server is running - no export needed for standalone server
