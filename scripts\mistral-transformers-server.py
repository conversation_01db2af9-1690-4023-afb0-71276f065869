#!/usr/bin/env python3

"""
Mistral Transformers Server
Alternative to vLLM for Windows using Transformers + PyTorch
"""

import os
import sys
import json
import base64
import io
from typing import List, Dict, Any, Optional
from PIL import Image
import torch
from transformers import AutoProcessor, AutoModelForVision2Seq
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Check if CUDA is available
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

# Initialize FastAPI app
app = FastAPI(title="Mistral Transformers Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for model and processor
model = None
processor = None
model_name = "mistralai/Pixtral-12B-2409"

# Pydantic models for API
class ChatMessage(BaseModel):
    role: str
    content: Any

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    max_tokens: Optional[int] = 4000
    temperature: Optional[float] = 0.1

class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str = "mistral-transformers"

def load_model():
    """Load the Mistral Pixtral model"""
    global model, processor
    
    print(f"Loading {model_name}...")
    print("This may take several minutes for first-time download...")
    
    try:
        # Load processor and model
        processor = AutoProcessor.from_pretrained(model_name)
        
        # Load model with appropriate settings
        if device == "cuda":
            model = AutoModelForVision2Seq.from_pretrained(
                model_name,
                torch_dtype=torch.float16,
                device_map="auto",
                trust_remote_code=True
            )
        else:
            model = AutoModelForVision2Seq.from_pretrained(
                model_name,
                torch_dtype=torch.float32,
                trust_remote_code=True
            )
            model = model.to(device)
        
        print(f"Model loaded successfully on {device}")
        return True

    except Exception as e:
        print(f"Failed to load model: {str(e)}")
        return False

def process_image_from_base64(base64_string: str) -> Image.Image:
    """Convert base64 string to PIL Image"""
    try:
        # Remove data URL prefix if present
        if base64_string.startswith("data:image"):
            base64_string = base64_string.split(",")[1]
        
        # Decode base64
        image_data = base64.b64decode(base64_string)
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary
        if image.mode != "RGB":
            image = image.convert("RGB")
            
        return image
    except Exception as e:
        raise ValueError(f"Failed to process image: {str(e)}")

@app.get("/v1/models")
async def list_models():
    """List available models"""
    return {
        "object": "list",
        "data": [
            ModelInfo(
                id=model_name,
                created=1699000000,
                owned_by="mistral-transformers"
            )
        ]
    }

@app.post("/v1/chat/completions")
async def chat_completions(request: ChatCompletionRequest):
    """Handle chat completion requests"""
    global model, processor
    
    if model is None or processor is None:
        raise HTTPException(status_code=503, detail="Model not loaded. Please wait for initialization.")
    
    try:
        # Process messages
        text_prompt = ""
        images = []
        
        for message in request.messages:
            if isinstance(message.content, list):
                # Handle multimodal content
                for part in message.content:
                    if part.get("type") == "text":
                        text_prompt += part["text"] + "\n"
                    elif part.get("type") == "image_url":
                        image_url = part["image_url"]["url"]
                        if image_url.startswith("data:image"):
                            image = process_image_from_base64(image_url)
                            images.append(image)
            else:
                # Handle text-only content
                text_prompt += str(message.content) + "\n"
        
        # Prepare inputs
        if images:
            # Vision + text processing
            inputs = processor(
                text=text_prompt.strip(),
                images=images,
                return_tensors="pt"
            ).to(device)
        else:
            # Text-only processing
            inputs = processor(
                text=text_prompt.strip(),
                return_tensors="pt"
            ).to(device)
        
        # Generate response
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=request.max_tokens,
                temperature=request.temperature,
                do_sample=True if request.temperature > 0 else False,
                pad_token_id=processor.tokenizer.eos_token_id
            )
        
        # Decode response
        generated_text = processor.decode(outputs[0], skip_special_tokens=True)
        
        # Remove the input prompt from the generated text
        if text_prompt.strip() in generated_text:
            response_text = generated_text.replace(text_prompt.strip(), "").strip()
        else:
            response_text = generated_text
        
        return {
            "id": f"chatcmpl-{hash(str(request.messages))}",
            "object": "chat.completion",
            "created": 1699000000,
            "model": request.model,
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": response_text
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": len(text_prompt.split()),
                "completion_tokens": len(response_text.split()),
                "total_tokens": len(text_prompt.split()) + len(response_text.split())
            }
        }
        
    except Exception as e:
        print(f"Error in chat completion: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "ok",
        "model": model_name,
        "device": device,
        "model_loaded": model is not None
    }

@app.on_event("startup")
async def startup_event():
    """Load model on startup"""
    print("Starting Mistral Transformers Server...")
    success = load_model()
    if not success:
        print("Model loading failed. Server will start but won't be functional.")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Mistral Transformers Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--model", default="mistralai/Pixtral-12B-2409", help="Model to load")
    
    args = parser.parse_args()
    
    # Set global model name
    model_name = args.model
    
    print(f"Starting server on {args.host}:{args.port}")
    print(f"Model: {model_name}")
    print(f"Device: {device}")
    
    uvicorn.run(app, host=args.host, port=args.port)
