#!/usr/bin/env python3

"""
Mistral Offline Server
Runs completely offline with locally downloaded models
NO INTERNET OR TOKENS REQUIRED FOR INFERENCE
"""

import os
import sys
import json
import base64
import io
from typing import List, Dict, Any, Optional
from PIL import Image
import torch
from transformers import AutoProcessor, AutoModelForVision2Seq
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Check if CUDA is available
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")

# Initialize FastAPI app
app = FastAPI(title="Mistral Offline Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for model and processor
model = None
processor = None
model_name = "mistralai/Pixtral-12B-2409"

# Pydantic models for API
class ChatMessage(BaseModel):
    role: str
    content: Any

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    max_tokens: Optional[int] = 4000
    temperature: Optional[float] = 0.1

class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str = "mistral-offline"

def check_offline_model():
    """Check if model is available offline"""
    try:
        # Try to load from cache without internet
        processor = AutoProcessor.from_pretrained(
            model_name, 
            local_files_only=True,
            trust_remote_code=True
        )
        return True
    except Exception as e:
        print(f"Model not available offline: {str(e)}")
        return False

def load_model_offline():
    """Load the Mistral Pixtral model in offline mode"""
    global model, processor
    
    print(f"Loading {model_name} in OFFLINE mode...")
    print("No internet connection required!")
    
    try:
        # Load processor (offline only)
        processor = AutoProcessor.from_pretrained(
            model_name,
            local_files_only=True,
            trust_remote_code=True
        )
        
        # Load model (offline only)
        if device == "cuda":
            model = AutoModelForVision2Seq.from_pretrained(
                model_name,
                torch_dtype=torch.float16,
                device_map="auto",
                local_files_only=True,
                trust_remote_code=True
            )
        else:
            model = AutoModelForVision2Seq.from_pretrained(
                model_name,
                torch_dtype=torch.float32,
                local_files_only=True,
                trust_remote_code=True
            )
            model = model.to(device)
        
        print(f"Model loaded successfully on {device} (OFFLINE)")
        return True
        
    except Exception as e:
        print(f"Failed to load model offline: {str(e)}")
        print("Model may not be downloaded yet. Run setup first.")
        return False

def process_image_from_base64(base64_string: str) -> Image.Image:
    """Convert base64 string to PIL Image"""
    try:
        # Remove data URL prefix if present
        if base64_string.startswith("data:image"):
            base64_string = base64_string.split(",")[1]
        
        # Decode base64
        image_data = base64.b64decode(base64_string)
        image = Image.open(io.BytesIO(image_data))
        
        # Convert to RGB if necessary
        if image.mode != "RGB":
            image = image.convert("RGB")
            
        return image
    except Exception as e:
        raise ValueError(f"Failed to process image: {str(e)}")

@app.get("/v1/models")
async def list_models():
    """List available models"""
    return {
        "object": "list",
        "data": [
            ModelInfo(
                id=model_name,
                created=1699000000,
                owned_by="mistral-offline"
            )
        ]
    }

@app.post("/v1/chat/completions")
async def chat_completions(request: ChatCompletionRequest):
    """Handle chat completion requests (OFFLINE)"""
    global model, processor
    
    if model is None or processor is None:
        raise HTTPException(
            status_code=503, 
            detail="Model not loaded. Run offline setup first: python mistral-offline-setup.py --download YOUR_TOKEN"
        )
    
    try:
        # Process messages
        text_prompt = ""
        images = []
        
        for message in request.messages:
            if isinstance(message.content, list):
                # Handle multimodal content
                for part in message.content:
                    if part.get("type") == "text":
                        text_prompt += part["text"] + "\n"
                    elif part.get("type") == "image_url":
                        image_url = part["image_url"]["url"]
                        if image_url.startswith("data:image"):
                            image = process_image_from_base64(image_url)
                            images.append(image)
            else:
                # Handle text-only content
                text_prompt += str(message.content) + "\n"
        
        # Prepare inputs (OFFLINE PROCESSING)
        if images:
            # Vision + text processing
            inputs = processor(
                text=text_prompt.strip(),
                images=images,
                return_tensors="pt"
            ).to(device)
        else:
            # Text-only processing
            inputs = processor(
                text=text_prompt.strip(),
                return_tensors="pt"
            ).to(device)
        
        # Generate response (OFFLINE)
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=request.max_tokens,
                temperature=request.temperature,
                do_sample=True if request.temperature > 0 else False,
                pad_token_id=processor.tokenizer.eos_token_id
            )
        
        # Decode response
        generated_text = processor.decode(outputs[0], skip_special_tokens=True)
        
        # Remove the input prompt from the generated text
        if text_prompt.strip() in generated_text:
            response_text = generated_text.replace(text_prompt.strip(), "").strip()
        else:
            response_text = generated_text
        
        return {
            "id": f"chatcmpl-{hash(str(request.messages))}",
            "object": "chat.completion",
            "created": 1699000000,
            "model": request.model,
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": response_text
                    },
                    "finish_reason": "stop"
                }
            ],
            "usage": {
                "prompt_tokens": len(text_prompt.split()),
                "completion_tokens": len(response_text.split()),
                "total_tokens": len(text_prompt.split()) + len(response_text.split())
            }
        }
        
    except Exception as e:
        print(f"Error in chat completion: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    offline_available = check_offline_model()
    return {
        "status": "ok",
        "model": model_name,
        "device": device,
        "model_loaded": model is not None,
        "offline_available": offline_available,
        "mode": "OFFLINE" if model is not None else "SETUP_REQUIRED"
    }

@app.on_event("startup")
async def startup_event():
    """Load model on startup (offline only)"""
    print("Starting Mistral Offline Server...")
    print("OFFLINE MODE - No internet or tokens required for inference")
    
    if check_offline_model():
        success = load_model_offline()
        if success:
            print("Ready for offline inference!")
        else:
            print("Model loading failed.")
    else:
        print("Model not downloaded yet.")
        print("Run: python mistral-offline-setup.py --download YOUR_TOKEN")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Mistral Offline Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--model", default="mistralai/Pixtral-12B-2409", help="Model to load")
    
    args = parser.parse_args()
    
    # Set global model name
    model_name = args.model
    
    print(f"Starting OFFLINE server on {args.host}:{args.port}")
    print(f"Model: {model_name}")
    print(f"Device: {device}")
    print("Mode: OFFLINE (no internet required)")
    
    uvicorn.run(app, host=args.host, port=args.port)
