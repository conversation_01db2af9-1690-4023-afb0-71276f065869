/********************************************************************
 *  abbreviatedTextExpander.js
 *  ---------------------------------------------------------------
 *  Main entry point for the Abbreviated Text Expander
 *  Now uses modular architecture for better maintainability
 *******************************************************************/

import { initializeAbbreviatedTextExpander } from './abbreviatedTextExpander/core.js';

/**
 * Initialize the Abbreviated Text Expander module
 * This is the main entry point that sets up all functionality
 */
export function initAbbreviatedTextExpander() {
    initializeAbbreviatedTextExpander();
}

// Auto-initialize when the module is loaded
initAbbreviatedTextExpander();
