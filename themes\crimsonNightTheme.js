
export const crimsonNightTheme = {
  name: 'Crimson Night',
  properties: {
    '--bg-primary': '#1A1A1A', // Very Dark Gray / Off-black
    '--bg-secondary': '#2B2B2B', // Dark Gray
    '--bg-app-container': '#1A1A1A',
    '--bg-panel': '#222222', // Slightly lighter panel
    '--bg-header': '#0D0D0D', // Almost Black Header
    '--bg-status-bar': '#0D0D0D',
    '--bg-modal-backdrop': 'rgba(0, 0, 0, 0.7)',
    '--bg-modal-content': '#2B2B2B',
    '--bg-input': '#2B2B2B',
    '--bg-input-disabled': '#3C3C3C',
    '--bg-list-box': '#222222',
    '--bg-list-item-hover': '#3C3C3C',
    '--bg-list-item-selected': '#DC143C', // Crimson Red
    '--bg-ssml-editor-view': '#1A1A1A',
    '--bg-ssml-instructions': '#2B2B2B',
    '--bg-ssml-preview-modal-content': '#222222',

    '--text-primary': '#E0E0E0', // Light Gray
    '--text-secondary': '#B0B0B0', // Softer Gray
    '--text-panel-heading': '#DC143C', // Crimson Red for headings
    '--text-header': '#E0E0E0',
    '--text-status-bar': '#E0E0E0',
    '--text-button-primary': '#FFFFFF', // White text on colored buttons
    '--text-list-item-selected': '#FFFFFF',
    '--text-placeholder': '#666666',
    '--text-highlighted': '#1A1A1A', // Dark text on red highlight
    '--text-search-highlighted': '#1A1A1A',
    '--text-ssml-editor-file-info': '#B0B0B0',
    '--text-ssml-instructions': '#E0E0E0',
    '--text-ssml-tag': '#DC143C', // Crimson Red
    '--text-ssml-attr-name': '#FF4500', // OrangeRed
    '--text-ssml-attr-value': '#FFD700', // Gold
    '--text-ssml-comment': '#666666',

    '--border-primary': '#3C3C3C', // Darker borders
    '--border-secondary': '#4D4D4D',
    '--border-input': '#DC143C', // Crimson Red border for inputs
    '--border-ssml-text-widget': '#DC143C',
    '--border-ssml-instructions': '#3C3C3C',
    '--border-ssml-preview-modal-content': '#3C3C3C',

    '--accent-primary': '#DC143C', // Crimson Red
    '--accent-secondary': '#FF4500', // OrangeRed
    '--accent-secondary-hover': '#E63E00', // Darker OrangeRed
    '--accent-playback': '#B22222', // Firebrick (darker red for playback)
    '--accent-playback-hover': '#A01F1F', // Darker Firebrick
    '--accent-highlight': '#DC143C', // Crimson Red highlight bg
    '--accent-search-highlight': '#FF4500', // OrangeRed search highlight bg
    '--accent-volume-slider': '#DC143C',

    '--button-primary-bg': '#8B0000', // DarkRed buttons
    '--button-primary-hover-bg': '#A52A2A', // Brown (lighter red)
    '--button-primary-active-bg': '#5C0000', // Darker Red active
    '--button-disabled-bg': '#404040',
    '--button-file-input-bg': '#DC143C', // Crimson for file inputs
    '--button-file-input-hover-bg': '#C71536',
    
    '--button-accent-bg': '#B22222', // Firebrick accent buttons
    '--button-accent-hover-bg': '#CC2F2F',
    '--button-warning-bg': '#FF4500', // OrangeRed for warning
    '--button-warning-hover-bg': '#FF6347', // Tomato (lighter orangered)
    '--button-info-bg': '#DC143C', // Crimson for info
    '--button-info-hover-bg': '#C71536',
    
    '--shadow-light': 'rgba(220, 20, 60, 0.1)',
    '--shadow-medium': 'rgba(220, 20, 60, 0.2)',
    '--shadow-modal': 'rgba(0, 0, 0, 0.3)',
    '--shadow-inset-active-nav': 'rgba(0,0,0,0.3)',

    '--icon-primary': '#DC143C', // Crimson Red icons
    '--icon-ms-tts': '#FF4500', // OrangeRed for MS TTS icon
    '--icon-selected-item': '#FFFFFF',

    '--status-processing-color': '#FF4500',
    '--status-ok-color': '#90EE90', // LightGreen (contrast against dark)
    '--status-flagged-color': '#FFD700', // Gold
    '--status-error-color': '#FF6347', // Tomato
    '--status-corrected-color': '#FF4500',
    '--status-no-text-color': '#666666',
  }
};