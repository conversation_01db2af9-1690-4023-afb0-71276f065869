# Environment Variables Example
# Copy this file to .env and fill in your actual API keys

# Google Cloud Text-to-Speech API Key
VITE_G_TTS_API_KEY=your_google_tts_api_key_here

# Microsoft Azure Text-to-Speech API Configuration
VITE_MS_TTS_API_KEY=your_microsoft_tts_api_key_here
VITE_MS_TTS_SERVICE_REGION=your_azure_region_here

# Local Whisper transcription (no API key needed)
# Whisper runs locally via Python server - see setup_whisper.py

# Note: 
# - Replace "your_*_here" with your actual API keys
# - Keep the VITE_ prefix for client-side access in Vite
# - Never commit your actual .env file with real API keys to version control
