/********************************************************************
 *  utils.js - Utility Functions Module
 *  ---------------------------------------------------------------
 *  Helper functions and utilities for the text expander
 *******************************************************************/

/**
 * Extract API key from various input formats
 */
export function extractApiKeyFromInput(input) {
    if (!input) return '';
    
    // If it's already a valid API key format, return as-is
    if (input.startsWith('AIza') && input.length >= 35 && input.length <= 45) {
        return input;
    }
    
    // Check if it's a Google AI Studio URL
    if (input.includes('aistudio.google.com') || input.includes('ai.google.dev')) {
        // Try to extract API key from URL parameters
        try {
            const url = new URL(input);
            const apiKey = url.searchParams.get('key') || url.searchParams.get('api_key');
            if (apiKey && apiKey.startsWith('AIza')) {
                return apiKey;
            }
        } catch (e) {
            // Not a valid URL, continue with other extraction methods
        }
        
        // Try to extract from URL hash or other parts
        const aiKeyMatch = input.match(/AIza[a-zA-Z0-9_-]{35,}/);
        if (aiKeyMatch) {
            return aiKeyMatch[0];
        }
    }
    
    // Try to extract API key pattern from any text
    const apiKeyPattern = /AIza[a-zA-Z0-9_-]{35,}/;
    const match = input.match(apiKeyPattern);
    if (match) {
        return match[0];
    }
    
    // If no valid API key found, return the original input
    // (this will trigger validation errors later)
    return input;
}

/**
 * Escape HTML characters
 */
export function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Escape special regex characters
 */
export function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Normalize quote text by handling hyphenation and line breaks
 */
export function normalizeQuoteText(text) {
    return text
        // Remove line breaks and extra spaces
        .replace(/\n/g, ' ')
        .replace(/\s+/g, ' ')
        // Handle hyphenated words split across lines
        .replace(/(\w+)-\s+(\w+)/g, '$1$2')
        // Clean up
        .trim();
}

/**
 * Count words in normalized text
 */
export function countWords(text) {
    return text.split(/\s+/).filter(word => word.length > 0).length;
}

/**
 * Convert Roman numeral to number
 */
export function romanToNumber(roman) {
    const romanNumerals = {
        'I': 1, 'V': 5, 'X': 10, 'L': 50, 'C': 100, 'D': 500, 'M': 1000
    };
    
    let result = 0;
    let prevValue = 0;
    
    for (let i = roman.length - 1; i >= 0; i--) {
        const currentValue = romanNumerals[roman[i]];
        
        if (currentValue < prevValue) {
            result -= currentValue;
        } else {
            result += currentValue;
        }
        
        prevValue = currentValue;
    }
    
    return result;
}

/**
 * Convert number to ordinal word
 */
export function numberToOrdinalWord(number) {
    const ordinals = {
        1: '1st', 2: '2nd', 3: '3rd', 4: '4th', 5: '5th',
        6: '6th', 7: '7th', 8: '8th', 9: '9th', 10: '10th',
        11: '11th', 12: '12th', 13: '13th', 14: '14th', 15: '15th',
        16: '16th', 17: '17th', 18: '18th', 19: '19th', 20: '20th',
        21: '21st', 22: '22nd', 23: '23rd', 24: '24th', 25: '25th',
        26: '26th', 27: '27th', 28: '28th', 29: '29th', 30: '30th'
    };
    
    // For numbers we have predefined
    if (ordinals[number]) {
        return ordinals[number];
    }
    
    // For larger numbers, construct the ordinal
    const lastDigit = number % 10;
    const lastTwoDigits = number % 100;
    
    // Special cases for 11th, 12th, 13th
    if (lastTwoDigits >= 11 && lastTwoDigits <= 13) {
        return `${number}th`;
    }
    
    // Regular cases
    switch (lastDigit) {
        case 1:
            return `${number}st`;
        case 2:
            return `${number}nd`;
        case 3:
            return `${number}rd`;
        default:
            return `${number}th`;
    }
}

/**
 * Format page ranges for audiobook narration
 */
export function formatPageRange(pages) {
    // Clean up the pages string
    const pageNumbers = pages.split(/[,\s]+/).filter(p => p && /^\d+$/.test(p)).map(Number);
    
    if (pageNumbers.length === 0) {
        return pages;
    }
    
    if (pageNumbers.length === 1) {
        return `page ${pageNumbers[0]}`;
    }
    
    // Check if it's a consecutive range
    const isConsecutive = pageNumbers.every((num, index) => 
        index === 0 || num === pageNumbers[index - 1] + 1
    );
    
    if (isConsecutive && pageNumbers.length > 2) {
        return `pages ${pageNumbers[0]} through ${pageNumbers[pageNumbers.length - 1]}`;
    } else if (pageNumbers.length === 2) {
        return `pages ${pageNumbers[0]} and ${pageNumbers[1]}`;
    } else {
        // For non-consecutive pages, list them
        const lastPage = pageNumbers.pop();
        return `pages ${pageNumbers.join(', ')}, and ${lastPage}`;
    }
}

/**
 * Check if text is a Latin phrase
 */
export function isLatinPhrase(text) {
    const latinPhrases = [
        'carpe diem', 'et cetera', 'ad hoc', 'ad infinitum', 'alma mater',
        'bona fide', 'circa', 'de facto', 'et al', 'ex libris', 'in situ',
        'ipso facto', 'per se', 'quid pro quo', 'sine qua non', 'status quo',
        'vice versa', 'vox populi', 'memento mori', 'tempus fugit',
        'alea iacta est', 'veni vidi vici', 'cogito ergo sum'
    ];
    
    const lowerText = text.toLowerCase();
    return latinPhrases.some(phrase => lowerText.includes(phrase));
}

/**
 * Translate common Latin phrases
 */
export function translateLatinPhrase(text) {
    const translations = {
        'carpe diem': 'seize the day',
        'quam minimum credula postero': 'put very little trust in tomorrow',
        'et cetera': 'and so forth',
        'ad hoc': 'for this purpose',
        'ad infinitum': 'to infinity',
        'alma mater': 'nourishing mother',
        'bona fide': 'in good faith',
        'circa': 'approximately',
        'de facto': 'in fact',
        'et al': 'and others',
        'ex libris': 'from the books',
        'in situ': 'in place',
        'ipso facto': 'by the fact itself',
        'per se': 'by itself',
        'quid pro quo': 'something for something',
        'sine qua non': 'without which nothing',
        'status quo': 'the existing state',
        'vice versa': 'the other way around',
        'vox populi': 'voice of the people',
        'memento mori': 'remember you must die',
        'tempus fugit': 'time flies',
        'alea iacta est': 'the die is cast',
        'veni vidi vici': 'I came, I saw, I conquered',
        'cogito ergo sum': 'I think, therefore I am'
    };
    
    const lowerText = text.toLowerCase();
    for (const [latin, english] of Object.entries(translations)) {
        if (lowerText.includes(latin)) {
            return english;
        }
    }
    
    return null;
}

/**
 * Calculate a score for formal/academic language
 */
export function calculateFormalLanguageScore(text) {
    let score = 0;
    
    // Academic/formal words and phrases
    const formalIndicators = [
        /\b(consequently|furthermore|moreover|nevertheless|therefore|thus|hence|whereby|wherein|whereas)\b/gi,
        /\b(indisputably|unquestionably|undoubtedly|certainly|evidently|clearly|obviously)\b/gi,
        /\b(pathologic|psychological|sociological|philosophical|theological|academic)\b/gi,
        /\b(expression|manifestation|phenomenon|principle|concept|theory|hypothesis)\b/gi,
        /\b(suppression|initiative|spontaneity|assertion|obedience|submission)\b/gi,
        /\b(medieval|modern|contemporary|historical|traditional|conventional)\b/gi
    ];
    
    for (const pattern of formalIndicators) {
        const matches = text.match(pattern);
        if (matches) {
            score += matches.length;
        }
    }
    
    // Long sentences (academic writing tends to have longer sentences)
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.split(/\s+/).length, 0) / sentences.length;
    if (avgSentenceLength > 20) {
        score += 1;
    }
    
    return score;
}

/**
 * Initialize dropdown functionality for buttons
 */
export function initializeDropdowns() {
    const dropdownButtons = document.querySelectorAll('.button-with-dropdown');
    
    dropdownButtons.forEach(container => {
        const button = container.querySelector('button');
        const dropdownToggle = container.querySelector('.dropdown-toggle');
        
        if (dropdownToggle) {
            dropdownToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                
                // Close other dropdowns
                dropdownButtons.forEach(otherContainer => {
                    if (otherContainer !== container) {
                        otherContainer.classList.remove('expanded');
                    }
                });
                
                // Toggle current dropdown
                container.classList.toggle('expanded');
            });
        }
        
        // Prevent button click when clicking dropdown toggle
        if (button && dropdownToggle) {
            button.addEventListener('click', (e) => {
                if (e.target === dropdownToggle || dropdownToggle.contains(e.target)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.button-with-dropdown')) {
            dropdownButtons.forEach(container => {
                container.classList.remove('expanded');
            });
        }
    });
}
