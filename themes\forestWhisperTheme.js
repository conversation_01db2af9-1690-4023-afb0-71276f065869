
export const forestWhisperTheme = {
  name: '<PERSON> Whisper',
  properties: {
    '--bg-primary': '#F1F8E9', // Very Light Green
    '--bg-secondary': '#FFFFFF', // White
    '--bg-app-container': '#FFFFFF',
    '--bg-panel': '#FAFFF5', // Off-white with green tint
    '--bg-header': '#388E3C', // Medium Green Header
    '--bg-status-bar': '#1B5E20', // Dark Green Status Bar
    '--bg-modal-backdrop': 'rgba(0, 0, 0, 0.4)',
    '--bg-modal-content': '#FFFFFF',
    '--bg-input': '#FFFFFF',
    '--bg-input-disabled': '#DCEDC8', // Light Green Disabled
    '--bg-list-box': '#FFFFFF',
    '--bg-list-item-hover': '#E6F5E0', // Lighter green hover
    '--bg-list-item-selected': '#4CAF50', // Standard Green for selected
    '--bg-ssml-editor-view': '#F1F8E9',
    '--bg-ssml-instructions': '#E6F5E0',
    '--bg-ssml-preview-modal-content': '#F5FCF0',

    '--text-primary': '#2E7D32', // Dark Green
    '--text-secondary': '#558B2F', // Medium Dark Green
    '--text-panel-heading': '#1B5E20', // Darkest Green for headings
    '--text-header': '#FFFFFF',
    '--text-status-bar': '#FFFFFF',
    '--text-button-primary': '#FFFFFF', // White text on colored buttons
    '--text-list-item-selected': '#FFFFFF',
    '--text-placeholder': '#7CB342', // Light Olive Green
    '--text-highlighted': '#1B5E20', // Dark text on light green highlight
    '--text-search-highlighted': '#1B5E20',
    '--text-ssml-editor-file-info': '#558B2F',
    '--text-ssml-instructions': '#2E7D32',
    '--text-ssml-tag': '#1B5E20', // Darkest Green for tags
    '--text-ssml-attr-name': '#D32F2F', // Muted Red (contrast)
    '--text-ssml-attr-value': '#7B1FA2', // Muted Purple (contrast)
    '--text-ssml-comment': '#7CB342',

    '--border-primary': '#A5D6A7', // Light Green borders
    '--border-secondary': '#C8E6C9',
    '--border-input': '#81C784', // Medium Green
    '--border-ssml-text-widget': '#81C784',
    '--border-ssml-instructions': '#A5D6A7',
    '--border-ssml-preview-modal-content': '#A5D6A7',

    '--accent-primary': '#4CAF50', // Standard Green
    '--accent-secondary': '#689F38', // Light Olive Green
    '--accent-secondary-hover': '#558B2F', // Darker Light Olive Green
    '--accent-playback': '#AED581', // Lighter Green for playback
    '--accent-playback-hover': '#9CCC65', // Darker Light Green
    '--accent-highlight': '#C8E6C9', // Very Light Green highlight
    '--accent-search-highlight': '#FFF59D', // Light Yellow
    '--accent-volume-slider': '#4CAF50',

    '--button-primary-bg': '#66BB6A', // Light Green buttons
    '--button-primary-hover-bg': '#5AAD5E',
    '--button-primary-active-bg': '#1B5E20', // Active nav button matches status bar
    '--button-disabled-bg': '#C8E6C9',
    '--button-file-input-bg': '#4CAF50', // Standard Green for file inputs
    '--button-file-input-hover-bg': '#43A047',
    
    '--button-accent-bg': '#689F38', // Light Olive Green
    '--button-accent-hover-bg': '#558B2F',
    '--button-warning-bg': '#EF6C00', // Orange (contrast)
    '--button-warning-hover-bg': '#E65100',
    '--button-info-bg': '#4CAF50', // Standard Green
    '--button-info-hover-bg': '#43A047',
    
    '--shadow-light': 'rgba(0,0,0,0.04)',
    '--shadow-medium': 'rgba(0,0,0,0.08)',
    '--shadow-modal': 'rgba(0,0,0,0.15)',
    '--shadow-inset-active-nav': 'rgba(0,0,0,0.1)',

    '--icon-primary': '#689F38', // Light Olive Green icons
    '--icon-ms-tts': '#4CAF50', // Standard Green for MS TTS icon
    '--icon-selected-item': '#FFFFFF',

    '--status-processing-color': '#4CAF50',
    '--status-ok-color': '#66BB6A',
    '--status-flagged-color': '#FFA726', // Orange
    '--status-error-color': '#EF5350', // Red
    '--status-corrected-color': '#689F38',
    '--status-no-text-color': '#7CB342',
  }
};