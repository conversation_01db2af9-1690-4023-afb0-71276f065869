// Using the 'jsdiff' library. Make sure to install it:
// npm install jsdiff
import * as Diff from 'diff';

/**
 * Escapes HTML special characters to prevent rendering issues and XSS.
 * @param {string} unsafe The string to escape.
 * @returns {string} The escaped string.
 */
function escapeHtml(unsafe) {
  if (!unsafe) return '';
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

/**
 * Extracts and normalizes text while preserving formatting context
 * @param {string} text The text to process
 * @returns {object} Object with cleanText and styleMap
 */
function extractAndNormalizeText(text) {
  if (!text) return { cleanText: '', styleMap: new Map() };

  // Extract text content while tracking positions of styled elements
  const styleMap = new Map();
  let cleanText = text;

  // Replace styled spans with placeholders to preserve context
  cleanText = cleanText.replace(/<span[^>]*>(.*?)<\/span>/g, (match, content, offset) => {
    const placeholder = `__STYLED_${offset}__`;
    styleMap.set(placeholder, { original: match, content });
    return placeholder;
  });

  // Replace other HTML tags with spaces to preserve word boundaries
  cleanText = cleanText.replace(/<[^>]*>/g, ' ');

  // Convert markdown bold to plain text
  cleanText = cleanText.replace(/\*\*(.*?)\*\*/g, '$1');

  // Normalize multiple spaces to single space
  cleanText = cleanText.replace(/\s+/g, ' ');

  // Trim leading/trailing whitespace
  cleanText = cleanText.trim();

  return { cleanText, styleMap };
}

/**
 * Normalizes text for better diff comparison by preserving word boundaries
 * @param {string} text The text to normalize
 * @returns {string} The normalized text
 */
function normalizeTextForDiff(text) {
  if (!text) return '';

  return text
    // Preserve word boundaries when removing tags
    .replace(/<[^>]*>/g, ' ') // Replace tags with space instead of nothing
    // Convert markdown bold to plain text
    .replace(/\*\*(.*?)\*\*/g, '$1')
    // Normalize multiple spaces to single space
    .replace(/\s+/g, ' ')
    // Trim leading/trailing whitespace
    .trim();
}

/**
 * Groups character-level changes into word-level changes for better display
 * @param {Array} charDiff Array of character-level diff parts
 * @returns {Array} Array of word-level diff parts
 */
function groupCharDiffByWords(charDiff) {
  const wordDiff = [];
  let currentPart = { value: '', added: false, removed: false };

  charDiff.forEach(part => {
    if (part.added === currentPart.added && part.removed === currentPart.removed) {
      // Same type of change, accumulate
      currentPart.value += part.value;
    } else {
      // Different type of change, start new part
      if (currentPart.value) {
        wordDiff.push(currentPart);
      }
      currentPart = {
        value: part.value,
        added: part.added || false,
        removed: part.removed || false
      };
    }
  });

  // Add the last part
  if (currentPart.value) {
    wordDiff.push(currentPart);
  }

  return wordDiff;
}

/**
 * Compares two strings and generates an HTML representation of the character-level differences.
 *
 * @param {string} originalText The original text.
 * @param {string} newText The new text to compare against the original.
 * @returns {string} An HTML string with <ins> and <del> tags highlighting the differences.
 */
export function generateDiffHtml(originalText, newText) {
  // Normalize both texts for comparison
  const normalizedOriginal = normalizeTextForDiff(originalText);
  const normalizedNew = normalizeTextForDiff(newText);

  console.log('Granular diff comparison:');
  console.log('Original (normalized):', normalizedOriginal.substring(0, 100) + '...');
  console.log('New (normalized):', normalizedNew.substring(0, 100) + '...');

  // Use diffChars for more granular comparison around formatted text
  const charDiff = Diff.diffChars(normalizedOriginal, normalizedNew);

  // Group the character changes by words for better display
  const wordDiff = groupCharDiffByWords(charDiff);

  let html = '';

  wordDiff.forEach(part => {
    const value = escapeHtml(part.value);
    if (part.added) {
      html += `<ins>${value}</ins>`;
    } else if (part.removed) {
      html += `<del>${value}</del>`;
    } else {
      html += value;
    }
  });

  return html;
}

/**
 * Generates a side-by-side diff comparison showing original and new text separately with granular precision
 * @param {string} originalText The original text
 * @param {string} newText The new text to compare against the original
 * @returns {object} Object with highlightedOriginal and highlightedNew properties
 */
export function generateSideBySideDiff(originalText, newText) {
  // Use enhanced normalization that preserves formatting context
  const { cleanText: normalizedOriginal } = extractAndNormalizeText(originalText);
  const { cleanText: normalizedNew } = extractAndNormalizeText(newText);

  console.log('Enhanced side-by-side diff comparison:');
  console.log('Original (enhanced normalized):', normalizedOriginal.substring(0, 100) + '...');
  console.log('New (enhanced normalized):', normalizedNew.substring(0, 100) + '...');

  // Use character-level diff for more precision
  const charDiff = Diff.diffChars(normalizedOriginal, normalizedNew);

  // Group character changes by words for better display
  const wordDiff = groupCharDiffByWords(charDiff);

  let highlightedOriginal = '';
  let highlightedNew = '';

  wordDiff.forEach(part => {
    const value = escapeHtml(part.value);

    if (part.added) {
      // Only show in the new text
      highlightedNew += `<ins>${value}</ins>`;
    } else if (part.removed) {
      // Only show in the original text
      highlightedOriginal += `<del>${value}</del>`;
    } else {
      // Show in both texts
      highlightedOriginal += value;
      highlightedNew += value;
    }
  });

  return {
    highlightedOriginal,
    highlightedNew
  };
}

/**
 * Specialized diff for AI text formatting that handles common patterns
 * @param {string} originalText The original text
 * @param {string} formattedText The AI-formatted text
 * @returns {object} Object with highlightedOriginal and highlightedNew properties
 */
export function generateFormattingAwareDiff(originalText, formattedText) {
  console.log('Formatting-aware diff starting...');

  try {
    // Validate inputs
    if (!originalText || !formattedText) {
      console.error('Invalid input texts:', { originalText: !!originalText, formattedText: !!formattedText });
      return {
        highlightedOriginal: originalText || '',
        highlightedNew: formattedText || ''
      };
    }

    console.log('Input validation passed');

    // Use the enhanced normalization
    console.log('Starting text normalization...');
    const { cleanText: cleanOriginal } = extractAndNormalizeText(originalText);
    const { cleanText: cleanFormatted } = extractAndNormalizeText(formattedText);

    console.log('Normalized texts:');
    console.log('- Original:', cleanOriginal.substring(0, 100) + '...');
    console.log('- Formatted:', cleanFormatted.substring(0, 100) + '...');

    // Perform character-level diff on the clean text
    console.log('Starting character-level diff...');
    const charDiff = Diff.diffChars(cleanOriginal, cleanFormatted);
    console.log('Character diff parts:', charDiff.length);

    console.log('Grouping character diff by words...');
    const wordDiff = groupCharDiffByWords(charDiff);
    console.log('Word diff parts:', wordDiff.length);

    let highlightedOriginal = '';
    let highlightedNew = '';

    wordDiff.forEach((part, index) => {
      const value = escapeHtml(part.value);

      if (part.added) {
        highlightedNew += `<ins>${value}</ins>`;
        console.log(`Part ${index}: Added "${value.substring(0, 20)}..."`);
      } else if (part.removed) {
        highlightedOriginal += `<del>${value}</del>`;
        console.log(`Part ${index}: Removed "${value.substring(0, 20)}..."`);
      } else {
        highlightedOriginal += value;
        highlightedNew += value;
        console.log(`Part ${index}: Unchanged "${value.substring(0, 20)}..."`);
      }
    });

    console.log('Formatting-aware diff completed successfully');
    console.log('Result lengths:', {
      highlightedOriginal: highlightedOriginal.length,
      highlightedNew: highlightedNew.length
    });

    return {
      highlightedOriginal,
      highlightedNew
    };
  } catch (error) {
    console.error('Error in generateFormattingAwareDiff:', error);
    // Return safe fallback
    return {
      highlightedOriginal: escapeHtml(originalText || ''),
      highlightedNew: escapeHtml(formattedText || '')
    };
  }
}