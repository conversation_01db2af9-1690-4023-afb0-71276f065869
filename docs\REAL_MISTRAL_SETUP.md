# 🚀 Real Mistral AI Setup Guide (Offline vLLM)

This guide shows you how to set up **actual Mistral AI with vision capabilities** using **offline vLLM deployment** for real OCR and text extraction from PDFs.

## 🎯 What You'll Get

- **Real AI Vision**: Mistral Pixtral-12B model with actual OCR capabilities
- **High Accuracy**: Professional-grade text extraction from images and PDFs
- **Offline Deployment**: Runs locally using Python and vLLM
- **No Docker Required**: Direct Python installation
- **OpenAI Compatible**: Drop-in replacement for OpenAI API

## 📋 Prerequisites

### System Requirements

- **RAM**: 16GB+ (32GB recommended for GPU)
- **Storage**: 30GB+ free space (for model download)
- **GPU**: NVIDIA GPU with 12GB+ VRAM (optional but recommended)
- **OS**: Windows 10/11, Linux, or macOS

### Software Requirements

1. **Python 3.8+** - [Download here](https://python.org/downloads/)
2. **NVIDIA Drivers** (for GPU) - [Download here](https://www.nvidia.com/drivers/)
3. **Hugging<PERSON>ace Account** - [Sign up here](https://huggingface.co/join)

## 🔧 Step-by-Step Setup

### Step 1: Install Python

**Windows:**
1. Download Python 3.8+ from https://python.org/downloads/
2. **Important**: Check "Add Python to PATH" during installation
3. Restart your computer after installation

**Mac:**
```bash
# Using Homebrew (recommended)
brew install python@3.11

# Or download from python.org
```

**Linux:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip python3-venv

# CentOS/RHEL
sudo yum install python3 python3-pip
```

### Step 2: Install NVIDIA Drivers (GPU Support)

**Windows:**
1. Download latest drivers from https://www.nvidia.com/drivers/
2. Install and restart

**Linux:**
```bash
# Ubuntu/Debian
sudo apt install nvidia-driver-535
sudo reboot

# Or use the official installer
wget https://developer.download.nvidia.com/compute/cuda/12.2.0/local_installers/cuda_12.2.0_535.54.03_linux.run
sudo sh cuda_12.2.0_535.54.03_linux.run
```

### Step 3: Install vLLM and Dependencies

**All Platforms:**
```bash
# Create a virtual environment (recommended)
python -m venv mistral-env

# Activate virtual environment
# Windows:
mistral-env\Scripts\activate
# Mac/Linux:
source mistral-env/bin/activate

# Install vLLM with vision support
pip install vllm[vision] torch torchvision --upgrade

# Install additional dependencies
pip install fastapi uvicorn pillow
```

### Step 4: Get HuggingFace Token

1. Go to https://huggingface.co/settings/tokens
2. Click "New token"
3. Name it "Mistral-Local"
4. Select "Read" permissions
5. Copy the token (starts with `hf_...`)

### Step 5: Accept Model License

1. Visit https://huggingface.co/mistralai/Pixtral-12B-2409
2. Click "Agree and access repository"
3. This gives you access to download the model

## 🚀 Quick Start

### Option 1: Automatic Setup (Recommended)

1. **Start the auto-start server**:
   ```bash
   # Make sure you're in your project directory
   node mistral-auto-server.js
   ```

2. **Open your PDF converter** at `http://localhost:5173`

3. **Select "Mistral Vision (Local)"** from OCR Engine

4. **The system will automatically**:
   - Check your system requirements
   - Prompt for HuggingFace token if needed
   - Download and start Mistral Pixtral-12B
   - Configure everything for you

### Option 2: Manual Docker Setup

If you prefer manual control:

```bash
# Set your HuggingFace token
export HF_TOKEN=your_token_here

# GPU version (recommended)
docker run -d --name mistral-server \
  --runtime nvidia --gpus all \
  -v ~/.cache/huggingface:/root/.cache/huggingface \
  --env "HUGGING_FACE_HUB_TOKEN=${HF_TOKEN}" \
  -p 8000:8000 \
  --ipc=host \
  vllm/vllm-openai:latest \
  --model mistralai/Pixtral-12B-2409 \
  --tokenizer_mode mistral \
  --load_format mistral \
  --config_format mistral \
  --limit_mm_per_prompt image=4

# CPU version (slower, no GPU needed)
docker run -d --name mistral-server \
  -v ~/.cache/huggingface:/root/.cache/huggingface \
  --env "HUGGING_FACE_HUB_TOKEN=${HF_TOKEN}" \
  -p 8000:8000 \
  --ipc=host \
  vllm/vllm-openai:latest \
  --model mistralai/Pixtral-12B-2409 \
  --tokenizer_mode mistral \
  --load_format mistral \
  --config_format mistral \
  --limit_mm_per_prompt image=4 \
  --max_model_len 8192 \
  --enforce_eager
```

## ⏱️ First-Time Setup Timeline

- **Model Download**: 15-30 minutes (24GB download)
- **Container Start**: 2-5 minutes
- **First Inference**: 30-60 seconds
- **Subsequent Uses**: Instant startup

## 🧪 Testing Your Setup

1. **Check if running**:
   ```bash
   curl http://localhost:8000/v1/models
   ```

2. **Test vision capabilities**:
   ```bash
   curl -X POST http://localhost:8000/v1/chat/completions \
     -H "Content-Type: application/json" \
     -d '{
       "model": "mistralai/Pixtral-12B-2409",
       "messages": [
         {
           "role": "user", 
           "content": [
             {"type": "text", "text": "What text do you see in this image?"},
             {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,..."}}
           ]
         }
       ]
     }'
   ```

3. **Use the test page**: Visit `http://localhost:5173/test-auto-start.html`

## 🔍 Troubleshooting

### Common Issues

**"Model not found" error:**
- Ensure you accepted the license at https://huggingface.co/mistralai/Pixtral-12B-2409
- Check your HuggingFace token has "Read" permissions

**"Out of memory" error:**
- Reduce `--max_model_len` to 8192 or 4096
- Use CPU mode instead of GPU
- Close other applications to free RAM

**"NVIDIA runtime not found":**
- Install NVIDIA Docker runtime
- Or use CPU mode (remove `--runtime nvidia --gpus all`)

**Slow performance:**
- Use GPU mode if available
- Increase `--max_model_len` if you have more VRAM
- Consider using a smaller model for faster inference

### Performance Tips

**GPU Optimization:**
- Use `--max_model_len 16384` or higher with sufficient VRAM
- Enable tensor parallelism: `--tensor_parallel_size 2` (for multi-GPU)

**CPU Optimization:**
- Use `--enforce_eager` for CPU mode
- Reduce `--max_model_len` to 4096 for faster loading
- Increase Docker memory allocation to 8GB+

## 🎉 Benefits of Real Mistral

- **Accurate OCR**: Professional-grade text extraction
- **Context Understanding**: Understands document structure
- **Multi-language**: Supports many languages
- **Table Extraction**: Can extract and format tables
- **Handwriting**: Can read some handwritten text
- **Layout Preservation**: Maintains document formatting

## 🔄 Switching Between Mock and Real

You can easily switch between mock and real Mistral:

1. **Mock Mode**: Fast testing, no setup required
2. **Real Mode**: Accurate results, requires setup

The auto-start system will:
- Try real Mistral first (if available)
- Fall back to mock mode if real setup fails
- Show clear indicators of which mode you're using

## 📊 Performance Comparison

| Feature | Mock Server | Real Mistral |
|---------|-------------|--------------|
| Setup Time | Instant | 30+ minutes |
| Accuracy | Simulated | Professional |
| Speed | Very Fast | Fast (GPU) / Slow (CPU) |
| Resource Usage | Minimal | High |
| Text Quality | Generic | Actual OCR |
| Cost | Free | Free (local) |

## 🎯 Next Steps

Once you have real Mistral running:

1. **Test with your PDFs** - Try different document types
2. **Optimize settings** - Adjust model parameters for your use case
3. **Monitor performance** - Check GPU/CPU usage and adjust accordingly
4. **Scale up** - Consider multiple GPUs or cloud deployment for production

---

**Ready to get started?** Run the auto-start server and let it guide you through the setup! 🚀
