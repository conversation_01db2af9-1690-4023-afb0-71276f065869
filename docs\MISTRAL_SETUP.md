# Mistral Local Vision Setup Guide

This guide explains how to set up Mistral AI locally with vision capabilities for the PDF to Text Converter.

## Overview

The PDF to Text Converter now supports two OCR engines:
- **Azure Document Intelligence** (Cloud-based)
- **Mistral Vision** (Local deployment)

## 🚀 **NEW: Auto-Start Feature**

The application now automatically starts a Mistral server when you switch to the Mistral Vision engine! This makes setup much easier.

### Quick Start (Recommended)

1. **Start the Auto-Start Server**:
   ```bash
   # Windows
   start-mistral-auto.bat

   # Linux/Mac
   chmod +x start-mistral-auto.sh
   ./start-mistral-auto.sh
   ```

2. **Open the PDF to Text Converter**:
   - Navigate to the PDF to Text Converter tab
   - Select "Mistral Vision (Local)" from the OCR Engine dropdown
   - The system will automatically:
     - Check if a Mistral server is running
     - Try to start a Docker container if available
     - Fall back to a mock server for testing
     - Show detailed status in the conversion log

3. **That's it!** The server will auto-start and you can begin converting PDFs.

## Prerequisites

- Docker installed on your system
- At least 8GB of RAM (16GB recommended)
- NVIDIA GPU with CUDA support (recommended for better performance)

## Option 1: Docker Deployment (Recommended)

### 1. Pull the Mistral Docker Image

```bash
# For CPU-only deployment
docker pull mistralai/mistral-inference:latest

# For GPU deployment (if you have NVIDIA GPU)
docker pull mistralai/mistral-inference:latest-gpu
```

### 2. Run Mistral Server

#### CPU Deployment:
```bash
docker run -d \
  --name mistral-server \
  -p 8000:8000 \
  -e MODEL_NAME=mistral-large-latest \
  mistralai/mistral-inference:latest
```

#### GPU Deployment:
```bash
docker run -d \
  --name mistral-server \
  --gpus all \
  -p 8000:8000 \
  -e MODEL_NAME=mistral-large-latest \
  mistralai/mistral-inference:latest-gpu
```

### 3. Verify Installation

Test the server is running:
```bash
curl http://localhost:8000/v1/models
```

You should see a JSON response with available models.

## Option 2: Manual Installation

### 1. Install Dependencies

```bash
pip install mistral-inference[vision]
```

### 2. Download Model

```bash
# Download the model (this may take some time)
python -c "
from mistral_inference.model import Transformer
from mistral_inference.generate import generate

# This will download the model on first run
model = Transformer.from_folder('mistral-large-latest')
"
```

### 3. Create Server Script

Create a file `mistral_server.py`:

```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Union
import base64
import io
from PIL import Image
from mistral_inference.model import Transformer
from mistral_inference.generate import generate
import uvicorn

app = FastAPI()

# Load model (this happens once at startup)
model = Transformer.from_folder('mistral-large-latest')

class ChatMessage(BaseModel):
    role: str
    content: Union[str, List[dict]]

class ChatRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    max_tokens: Optional[int] = 4000
    temperature: Optional[float] = 0.1

class ChatResponse(BaseModel):
    choices: List[dict]

@app.get("/v1/models")
async def list_models():
    return {
        "data": [
            {
                "id": "mistral-large-latest",
                "object": "model",
                "created": 1234567890,
                "owned_by": "mistral"
            }
        ]
    }

@app.post("/v1/chat/completions")
async def chat_completions(request: ChatRequest):
    try:
        # Process the request and generate response
        # This is a simplified implementation
        # You'll need to implement the actual vision processing here
        
        response = {
            "choices": [
                {
                    "message": {
                        "role": "assistant",
                        "content": "Text extraction result would go here"
                    }
                }
            ]
        }
        
        return response
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 4. Run the Server

```bash
python mistral_server.py
```

## Configuration in the Application

### 1. Environment Variables

Update your `.env.local` file:

```env
# Mistral Local Configuration
VITE_MISTRAL_URL=http://localhost:8000
VITE_MISTRAL_API_KEY=
VITE_MISTRAL_MODEL=mistral-large-latest
```

### 2. Application Settings

1. Open the PDF to Text Converter tab
2. Select "Mistral Vision (Local)" from the OCR Engine dropdown
3. Configure the Mistral settings:
   - **Server URL**: `http://localhost:8000` (default)
   - **API Key**: Leave empty for local deployment
   - **Model**: `mistral-large-latest` (default)
4. Click "Test Connection" to verify the setup

## Usage

1. **Select OCR Engine**: Choose "Mistral Vision (Local)" from the dropdown
2. **Upload PDF**: Select your PDF file
3. **Configure Options**: Set DPI and other processing options
4. **Start Conversion**: Click "Start Conversion"
5. **Download Result**: Download the extracted text as a Word document

## Performance Considerations

### CPU vs GPU
- **CPU**: Slower but works on any system
- **GPU**: Much faster, requires NVIDIA GPU with CUDA

### Memory Requirements
- **Minimum**: 8GB RAM
- **Recommended**: 16GB+ RAM for better performance
- **GPU**: 6GB+ VRAM for GPU acceleration

### Model Selection
- **mistral-large-latest**: Best quality, slower
- **mistral-medium-latest**: Good balance
- **mistral-small-latest**: Faster, lower quality

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check if Docker container is running: `docker ps`
   - Verify port 8000 is not in use: `netstat -an | grep 8000`
   - Check firewall settings

2. **Out of Memory**
   - Reduce batch size
   - Use smaller model
   - Add more RAM/swap

3. **Slow Performance**
   - Use GPU acceleration
   - Reduce image DPI
   - Use smaller model

### Logs

Check Docker logs:
```bash
docker logs mistral-server
```

## Comparison: Azure vs Mistral

| Feature | Azure DI | Mistral Vision |
|---------|----------|----------------|
| **Setup** | Easy (cloud) | Complex (local) |
| **Cost** | Pay per use | Free after setup |
| **Privacy** | Data sent to cloud | Fully local |
| **Performance** | Fast | Depends on hardware |
| **Accuracy** | Very high | Good |
| **Languages** | 100+ languages | Primarily English |

## Security Notes

- Mistral runs locally, so your documents never leave your machine
- No API keys required for local deployment
- All processing happens on your hardware
- Perfect for sensitive documents

## Support

For issues with:
- **Mistral setup**: Check [Mistral documentation](https://docs.mistral.ai/)
- **Application integration**: Check the browser console for errors
- **Performance**: Monitor system resources during processing

## Next Steps

1. Test with a small PDF first
2. Monitor performance and adjust settings
3. Consider GPU acceleration for better speed
4. Experiment with different models for your use case
