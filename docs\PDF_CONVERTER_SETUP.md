# PDF to Text Converter Setup Guide

## Quick Start

The PDF to Text Converter supports three OCR engines. Choose the one that works best for your setup:

### 🚀 Option 1: Ollama (Recommended - No Authentication Required)

**Pros:** Free, runs locally, no API keys needed, good accuracy
**Cons:** Requires ~4-8GB disk space for models

1. **Download Ollama:**
   - Visit https://ollama.ai/download
   - Download and install for your operating system
   - Restart your computer after installation

2. **Install a Vision Model:**
   ```bash
   ollama pull llava:7b
   ```
   
   **Alternative models:**
   - `moondream:1.8b` - Faster but may describe images instead of extracting text
   - `llava:13b` - Higher quality but slower
   - `bakllava:7b` - Alternative option

3. **Start the Application:**
   ```bash
   npm run dev
   ```

4. **Use the Converter:**
   - Select "Ollama Vision" as OCR Engine
   - Upload a PDF file
   - Click "Start Conversion"

### ☁️ Option 2: Azure Document Intelligence (Cloud)

**Pros:** Very accurate, handles complex layouts well
**Cons:** Requires Azure subscription and API costs

1. **Create Azure Resource:**
   - Go to Azure Portal
   - Create a "Document Intelligence" resource
   - Copy the endpoint URL and API key

2. **Configure in App:**
   - Select "Azure Document Intelligence" as OCR Engine
   - Enter your endpoint and API key
   - Click "Test Connection"

### 🤖 Option 3: Mistral Vision (Local AI)

**Pros:** High accuracy, runs locally
**Cons:** Complex setup, requires ~24GB disk space

1. **Start with Backend:**
   ```bash
   npm run dev  # This starts the required backend server
   ```

2. **Configure Mistral:**
   - Select "Mistral Vision" as OCR Engine
   - The app will attempt to auto-start a mock server
   - For real Mistral, follow the setup wizard in the app

## Troubleshooting

### "Conversion not working" - Common Issues:

1. **Backend Server Not Running:**
   - Make sure you started with `npm run dev` (not `npm run dev:vite-only`)
   - Check that port 3001 is available

2. **Ollama Issues:**
   - Verify Ollama is installed: `ollama --version`
   - Check if models are installed: `ollama list`
   - Restart Ollama service if needed

3. **PDF Not Loading:**
   - Ensure the PDF file is not corrupted
   - Try with a smaller PDF first
   - Check browser console for errors

4. **Slow Processing:**
   - Ollama: Enable "Fast Mode" checkbox
   - Try smaller models like `moondream:1.8b`
   - Reduce image DPI to 150

### Error Messages:

- **"Backend server not available on port 3001"**
  - Solution: Start with `npm run dev` instead of `npm run dev:vite-only`

- **"Ollama not available"**
  - Solution: Install Ollama and pull a vision model

- **"No text content found"**
  - Try different OCR engines
  - Check if PDF contains actual text (not just images)
  - Increase image DPI to 600

## Performance Tips

### For Ollama:
- **Fast Setup:** Use `moondream:1.8b` model
- **Balanced:** Use `llava:7b` model (recommended)
- **High Quality:** Use `llava:13b` model

### For All Engines:
- Start with small PDFs (1-5 pages) for testing
- Use 300 DPI for most documents
- Enable "Fast Mode" when available

## File Output

The converter creates Word documents (.docx) with:
- Extracted text organized by page
- Processing metadata (OCR engine used, processing time)
- Error information for failed pages

## Need Help?

1. Check the conversion log in the app for detailed error messages
2. Try switching to a different OCR engine
3. Verify your setup using the "Test Connection" buttons
4. Start with a simple, text-heavy PDF for testing

## System Requirements

- **Ollama:** 4-8GB free disk space, 4GB+ RAM
- **Azure:** Internet connection, Azure subscription
- **Mistral:** 24GB+ disk space, 8GB+ RAM, Python 3.8+
