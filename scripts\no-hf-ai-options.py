#!/usr/bin/env python3

"""
No-HuggingFace AI Options
Multiple ways to get real AI without HuggingFace authentication
"""

import os
import sys
import requests
import subprocess
from pathlib import Path

def check_ollama():
    """Check if Ollama is installed"""
    try:
        result = subprocess.run(['ollama', '--version'], capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False

def install_ollama():
    """Install Ollama"""
    print("Installing Ollama...")
    
    if os.name == 'nt':  # Windows
        print("Please download Ollama from: https://ollama.ai/download/windows")
        print("Run the installer and restart this script")
        return False
    else:  # Linux/Mac
        try:
            subprocess.run(['curl', '-fsSL', 'https://ollama.ai/install.sh'], check=True)
            return True
        except subprocess.CalledProcessError:
            print("Failed to install Ollama automatically")
            print("Please visit: https://ollama.ai/download")
            return False

def download_ollama_model(model_name):
    """Download a model with Ollama"""
    print(f"Downloading {model_name} with Ollama...")
    try:
        subprocess.run(['ollama', 'pull', model_name], check=True)
        print(f"Successfully downloaded {model_name}")
        return True
    except subprocess.CalledProcessError:
        print(f"Failed to download {model_name}")
        return False

def test_ollama_model(model_name):
    """Test an Ollama model"""
    print(f"Testing {model_name}...")
    try:
        result = subprocess.run([
            'ollama', 'run', model_name, 
            'Extract text from this image and format it nicely'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("Model is working!")
            return True
        else:
            print("Model test failed")
            return False
    except subprocess.TimeoutExpired:
        print("Model test timed out")
        return False

def download_mistral_nemo_direct():
    """Download Mistral Nemo directly from Mistral"""
    print("Downloading Mistral Nemo 12B directly from Mistral...")
    print("No HuggingFace account required!")
    
    url = "https://models.mistralcdn.com/mistral-nemo-2407/mistral-nemo-instruct-2407.tar"
    
    # This would be a large download - just show the option
    print(f"Direct download URL: {url}")
    print("Size: ~24GB")
    print("Capabilities: Text generation, instruction following")
    print("Limitation: No vision capabilities")
    
    return True

def main():
    print("🚀 No-HuggingFace AI Options")
    print("=" * 40)
    print()
    
    print("Here are your options for real AI without HuggingFace authentication:")
    print()
    
    # Option 1: Ollama with Vision Models
    print("📋 Option 1: Ollama (Recommended)")
    print("- Easy installation and management")
    print("- Multiple vision models available")
    print("- No authentication required")
    print("- Models: LLaVA, Bakllava, Moondream")
    print()
    
    if check_ollama():
        print("✅ Ollama is already installed!")
        
        # Show available vision models
        vision_models = [
            "llava:7b",
            "llava:13b", 
            "bakllava:7b",
            "moondream:1.8b"
        ]
        
        print("Available vision models:")
        for i, model in enumerate(vision_models, 1):
            print(f"  {i}. {model}")
        
        print()
        choice = input("Download a vision model? (1-4, or 'n' for no): ")
        
        if choice.isdigit() and 1 <= int(choice) <= len(vision_models):
            model = vision_models[int(choice) - 1]
            download_ollama_model(model)
            
    else:
        print("❌ Ollama not installed")
        install_choice = input("Install Ollama? (y/n): ")
        if install_choice.lower() == 'y':
            install_ollama()
    
    print()
    
    # Option 2: Direct Mistral Downloads
    print("📋 Option 2: Direct Mistral Downloads")
    print("- Download directly from Mistral servers")
    print("- No authentication required")
    print("- Apache 2.0 license")
    print("- Text-only models (no vision)")
    print()
    
    mistral_models = {
        "Mistral 7B Instruct": "https://models.mistralcdn.com/mistral-7b-v0-3/mistral-7B-Instruct-v0.3.tar",
        "Mistral Nemo 12B": "https://models.mistralcdn.com/mistral-nemo-2407/mistral-nemo-instruct-2407.tar",
        "Codestral 22B": "https://models.mistralcdn.com/codestral-22b-v0-1/codestral-22B-v0.1.tar"
    }
    
    print("Available models:")
    for i, (name, url) in enumerate(mistral_models.items(), 1):
        print(f"  {i}. {name}")
    
    print()
    
    # Option 3: Alternative Approaches
    print("📋 Option 3: Alternative Approaches")
    print("- Use cloud APIs (Google Vision, Azure)")
    print("- Use specialized OCR tools (Tesseract, PaddleOCR)")
    print("- Use browser-based AI (WebLLM)")
    print()
    
    # Option 4: The HuggingFace Reality
    print("📋 Option 4: Why HuggingFace is Usually Required")
    print("- Most advanced vision models are gated")
    print("- License agreements protect model creators")
    print("- Free account creation takes 2 minutes")
    print("- Token is only needed for initial download")
    print("- After download: completely offline")
    print()
    
    print("🎯 Recommendations:")
    print()
    print("For Vision + OCR:")
    print("1. Ollama + LLaVA (easiest, no auth)")
    print("2. Get HuggingFace token for Pixtral-12B (best quality)")
    print()
    print("For Text Only:")
    print("1. Direct Mistral downloads (no auth)")
    print("2. Ollama text models")
    print()
    
    print("💡 Pro Tip:")
    print("HuggingFace account is free and takes 2 minutes to create.")
    print("The token is only needed once for download.")
    print("After that, everything runs completely offline!")

if __name__ == "__main__":
    main()
