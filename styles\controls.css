
.controls-panel {
    flex: 0 0 300px; 
    gap: 15px; 
}

.controls-panel .file-loader-section,
.controls-panel .audio-controls-section,
.controls-panel .tts-reprocessor-section,
.controls-panel .verification-controls-section {
    display: flex;
    flex-direction: column;
    gap: 10px; 
    flex-shrink: 0; 
    min-height: 0; 
}

.verification-controls-section h3 {
    font-size: 1em;
    margin-top: 5px;
    margin-bottom: 5px;
    flex-shrink: 0;
    color: var(--text-panel-heading);
    padding-bottom: 5px;
    border-bottom: 1px solid var(--border-primary);
}

.file-loader-section .file-input-label {
    margin-bottom: 5px; 
}
.file-loader-section .file-input-label:last-of-type {
    margin-bottom: 0; 
}

.file-input-label {
    background-color: var(--button-file-input-bg);
    color: var(--text-button-primary);
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    font-size: 0.9em;
    transition: background-color 0.2s;
    display: block;
}

.file-input-label:hover {
    background-color: var(--button-file-input-hover-bg);
}

input[type="file"] {
    display: none;
}

.button-group {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: wrap;
}

.button-group button, .control-button {
    background-color: var(--button-primary-bg);
    color: var(--text-button-primary);
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9em;
    transition: background-color 0.2s, opacity 0.2s;
    width: 100%; /* Make buttons in controls full width */
    box-sizing: border-box;
    justify-content: center; /* Center text in button */
}

.button-group button .material-icons, .control-button .material-icons {
    font-size: 1.1em;
}

.button-group button:hover:not(:disabled), .control-button:hover:not(:disabled) {
    background-color: var(--button-primary-hover-bg);
}

.button-group button:disabled, .control-button:disabled {
    background-color: var(--button-disabled-bg);
    cursor: not-allowed; 
    opacity: 0.65;
    pointer-events: none; 
}

.control-button.accent-button {
    background-color: var(--button-accent-bg); 
    font-weight: bold;
}
.control-button.accent-button:hover:not(:disabled) {
    background-color: var(--button-accent-hover-bg);
}
.control-button.warning-button {
    background-color: var(--button-warning-bg); 
}
.control-button.warning-button:hover:not(:disabled) {
    background-color: var(--button-warning-hover-bg);
}
.control-button.info-button {
    background-color: var(--button-info-bg); 
}
.control-button.info-button:hover:not(:disabled) {
    background-color: var(--button-info-hover-bg);
}

.playback-controls button {
    background-color: var(--accent-playback);
    width: auto; /* Override full width for these specific buttons */
}

.playback-controls button:hover:not(:disabled) {
    background-color: var(--accent-playback-hover); 
}

.progress-section {
    margin-top: 0; 
}

.progress-bar {
    width: 100%;
    accent-color: var(--accent-primary);
    cursor: pointer;
}

.time-display {
    display: flex;
    justify-content: space-between;
    font-size: 0.8em;
    color: var(--text-secondary);
    margin-top: 5px;
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 0; 
}

.volume-slider {
    flex-grow: 1;
    accent-color: var(--accent-volume-slider);
    cursor: pointer;
}

.break-buttons button {
    background-color: var(--accent-secondary);
    font-size: 0.85em;
    width: auto; /* Override full width */
}

.break-buttons button:hover:not(:disabled) {
    background-color: var(--accent-secondary-hover);
}

.audio-controls-section .button-group,
.audio-controls-section .progress-section,
.audio-controls-section .volume-control {
    margin-bottom: 0; 
}

.tts-voice-selector {
    display: flex;
    flex-direction: column; 
    gap: 5px; 
}

#reprocess-ms-tts-btn {
    margin-top: 10px; 
}
.tts-reprocessor-section .tts-voice-selector:not(:last-child) {
    margin-bottom: 10px;
}

.tts-voice-selector label {
    font-size: 0.9em;
    color: var(--text-panel-heading);
}

.tts-voice-selector select {
    padding: 8px 10px;
    border-radius: 4px;
    border: 1px solid var(--border-input);
    font-size: 0.9em;
    background-color: var(--bg-input);
    color: var(--text-primary);
    cursor: pointer;
}

.tts-voice-selector select:disabled {
    background-color: var(--bg-input-disabled);
    cursor: not-allowed;
}