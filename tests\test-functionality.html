<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote and Footnote Inserter - Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .test-text {
            background: #f8f9fa;
            padding: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .annotation-list {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
        }
        .annotation-item {
            background: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 8px;
            margin: 5px 0;
            cursor: pointer;
        }
        .annotation-item:hover {
            background: #e9ecef;
        }
        .annotation-item.selected {
            background: #007bff;
            color: white;
        }
        .annotation-type {
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Quote and Footnote Inserter - Functionality Test</h1>
        <p>This page tests the core functionality of the Quote and Footnote Inserter.</p>
        
        <div class="test-result info">
            <h3>📝 Sample Text</h3>
            <textarea id="sample-text" rows="6" style="width: 100%; font-family: monospace;">This is a sample text with "quoted content" and footnote some important note end footnote for testing purposes. Here's another "quote to test" and footnote another note end footnote.

This paragraph has "multiple quotes" and "more quoted text" along with footnote first note end footnote and footnote second note end footnote.</textarea>
        </div>
        
        <div class="controls">
            <button onclick="extractAnnotations()">Extract Annotations</button>
            <button onclick="addQuoteMarkers()">Add Quote Markers</button>
            <button onclick="addFootnoteMarkers()">Add Footnote Markers</button>
            <button onclick="clearSelection()">Clear Selection</button>
        </div>
        
        <div class="test-result info">
            <h3>📋 Extracted Annotations</h3>
            <div id="annotations-list" class="annotation-list">
                <p>Click "Extract Annotations" to see results...</p>
            </div>
        </div>
        
        <div class="test-result info">
            <h3>📊 Modified Text</h3>
            <div id="modified-text" class="test-text">Modified text will appear here...</div>
        </div>
        
        <div class="test-result info">
            <h3>🔗 Quick Links</h3>
            <button onclick="openMainApp()">Open Main Application</button>
            <button onclick="openRegexTest()">Open Regex Test</button>
        </div>
    </div>

    <script>
        // Global variables
        let annotations = [];
        let selectedAnnotationIndex = null;
        
        // Regex pattern for finding annotations
        const ANNOTATION_PATTERN = /[""]([^""]*?)[""]|footnote\s+(.*?)\s+end footnote/gi;
        const MARKER_TEXTS = new Set(["quote", "end quote", "footnote", "end footnote"]);
        
        function getAnnotationsFromText(text) {
            const foundAnnotations = [];
            let match;
            
            ANNOTATION_PATTERN.lastIndex = 0;
            
            while ((match = ANNOTATION_PATTERN.exec(text)) !== null) {
                let annotationType, annotationText, startPos, endPos;
                
                if (match[1] !== undefined) {
                    annotationType = "Quote";
                    annotationText = match[1];
                    startPos = match.index + match[0].indexOf(match[1]);
                    endPos = startPos + match[1].length;
                } else if (match[2] !== undefined) {
                    annotationType = "Footnote";
                    annotationText = match[2];
                    startPos = match.index + match[0].indexOf(match[2]);
                    endPos = startPos + match[2].length;
                }
                
                if (annotationText && !MARKER_TEXTS.has(annotationText.toLowerCase().trim())) {
                    foundAnnotations.push({
                        type: annotationType,
                        text: annotationText,
                        start: startPos,
                        end: endPos,
                        fullStart: match.index,
                        fullEnd: match.index + match[0].length
                    });
                }
            }
            
            foundAnnotations.sort((a, b) => a.start - b.start);
            return foundAnnotations;
        }
        
        function extractAnnotations() {
            const text = document.getElementById('sample-text').value;
            annotations = getAnnotationsFromText(text);
            displayAnnotations();
        }
        
        function displayAnnotations() {
            const listContainer = document.getElementById('annotations-list');
            listContainer.innerHTML = '';
            
            if (annotations.length === 0) {
                listContainer.innerHTML = '<p>No annotations found.</p>';
                return;
            }
            
            annotations.forEach((annotation, index) => {
                const item = document.createElement('div');
                item.className = 'annotation-item';
                item.dataset.index = index;
                item.innerHTML = `
                    <div class="annotation-type">${annotation.type}</div>
                    <div>${annotation.text}</div>
                `;
                item.addEventListener('click', () => selectAnnotation(index));
                listContainer.appendChild(item);
            });
        }
        
        function selectAnnotation(index) {
            // Remove previous selection
            document.querySelectorAll('.annotation-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // Add selection to new item
            const item = document.querySelector(`[data-index="${index}"]`);
            if (item) {
                item.classList.add('selected');
                selectedAnnotationIndex = index;
            }
        }
        
        function addQuoteMarkers() {
            addMarkers('quote');
        }
        
        function addFootnoteMarkers() {
            addMarkers('footnote');
        }
        
        function addMarkers(markerType) {
            if (selectedAnnotationIndex === null) {
                alert('Please select an annotation first.');
                return;
            }
            
            const annotation = annotations[selectedAnnotationIndex];
            const textArea = document.getElementById('sample-text');
            const currentText = textArea.value;
            
            const startMarker = `${markerType}, `;
            const endMarker = ` end ${markerType},`;
            
            const newText = 
                currentText.substring(0, annotation.fullStart) +
                startMarker +
                currentText.substring(annotation.fullStart, annotation.fullEnd) +
                endMarker +
                currentText.substring(annotation.fullEnd);
            
            textArea.value = newText;
            document.getElementById('modified-text').textContent = newText;
            
            // Re-extract annotations
            extractAnnotations();
        }
        
        function clearSelection() {
            selectedAnnotationIndex = null;
            document.querySelectorAll('.annotation-item').forEach(item => {
                item.classList.remove('selected');
            });
        }
        
        function openMainApp() {
            window.open('http://localhost:8000', '_blank');
        }
        
        function openRegexTest() {
            window.open('http://localhost:8000/tests/test-quote-footnote.html', '_blank');
        }
        
        // Initialize
        extractAnnotations();
    </script>
</body>
</html>
