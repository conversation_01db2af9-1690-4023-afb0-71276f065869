<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>Navigation Debug Test</h1>
    
    <div class="status" id="status">
        Testing navigation functionality...
    </div>
    
    <div>
        <button class="test-button" onclick="testNavigationButtons()">Test Navigation Buttons</button>
        <button class="test-button" onclick="testDOMElements()">Test DOM Elements</button>
        <button class="test-button" onclick="testEventListeners()">Test Event Listeners</button>
        <button class="test-button" onclick="openMainApp()">Open Main App</button>
    </div>
    
    <div id="results"></div>
    
    <script>
        function updateStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = isError ? 'status error' : 'status success';
        }
        
        function addResult(message) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.style.margin = '5px 0';
            div.style.padding = '5px';
            div.style.background = '#e9ecef';
            div.style.borderRadius = '3px';
            div.textContent = message;
            results.appendChild(div);
        }
        
        function testNavigationButtons() {
            updateStatus('Testing navigation buttons...');
            
            // Test if we can access the main app's window
            try {
                const mainWindow = window.open('http://localhost:5174', '_blank');
                if (mainWindow) {
                    addResult('✅ Main app window opened successfully');
                    
                    // Wait a bit for the page to load, then test
                    setTimeout(() => {
                        try {
                            const buttons = mainWindow.document.querySelectorAll('.header-nav-button');
                            addResult(`Found ${buttons.length} navigation buttons`);
                            
                            buttons.forEach((btn, index) => {
                                addResult(`Button ${index + 1}: ${btn.id} - ${btn.textContent}`);
                                
                                // Test if button is clickable
                                const rect = btn.getBoundingClientRect();
                                const isVisible = rect.width > 0 && rect.height > 0;
                                addResult(`  - Visible: ${isVisible}`);
                                addResult(`  - Disabled: ${btn.disabled}`);
                                addResult(`  - Has click listener: ${btn.onclick !== null || btn.addEventListener !== undefined}`);
                            });
                            
                            updateStatus('Navigation button test completed');
                        } catch (e) {
                            addResult('❌ Error accessing main app DOM: ' + e.message);
                            updateStatus('Cross-origin access blocked - this is normal', true);
                        }
                    }, 2000);
                } else {
                    addResult('❌ Failed to open main app window');
                    updateStatus('Failed to open main app', true);
                }
            } catch (e) {
                addResult('❌ Error: ' + e.message);
                updateStatus('Test failed: ' + e.message, true);
            }
        }
        
        function testDOMElements() {
            updateStatus('Testing DOM element access...');
            
            // Test basic DOM functionality
            const testDiv = document.createElement('div');
            testDiv.id = 'test-element';
            testDiv.textContent = 'Test Element';
            document.body.appendChild(testDiv);
            
            const found = document.getElementById('test-element');
            if (found) {
                addResult('✅ Basic DOM manipulation works');
                document.body.removeChild(testDiv);
            } else {
                addResult('❌ Basic DOM manipulation failed');
            }
            
            // Test event listener
            const testBtn = document.createElement('button');
            testBtn.textContent = 'Test Button';
            testBtn.addEventListener('click', () => {
                addResult('✅ Event listener fired successfully');
            });
            
            document.body.appendChild(testBtn);
            addResult('Test button added - click it to test event listeners');
            
            updateStatus('DOM element test completed');
        }
        
        function testEventListeners() {
            updateStatus('Testing event listener functionality...');
            
            // Create a test button with multiple event types
            const testBtn = document.createElement('button');
            testBtn.textContent = 'Multi-Event Test Button';
            testBtn.className = 'test-button';
            
            let clickCount = 0;
            
            testBtn.addEventListener('click', () => {
                clickCount++;
                addResult(`✅ Click event ${clickCount} fired`);
            });
            
            testBtn.addEventListener('mousedown', () => {
                addResult('✅ Mousedown event fired');
            });
            
            testBtn.addEventListener('mouseup', () => {
                addResult('✅ Mouseup event fired');
            });
            
            document.body.appendChild(testBtn);
            addResult('Multi-event test button added - interact with it');
            
            updateStatus('Event listener test setup completed');
        }
        
        function openMainApp() {
            window.open('http://localhost:5174', '_blank');
        }
        
        // Auto-run basic test on load
        window.addEventListener('load', () => {
            addResult('🚀 Debug test page loaded');
            addResult('Click the test buttons above to diagnose navigation issues');
        });
    </script>
</body>
</html>
