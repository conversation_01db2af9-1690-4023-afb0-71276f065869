# Local Whisper Setup Guide

## Quick Start

### 1. Automated Setup (Recommended)
```bash
# Install everything automatically
python setup_whisper.py install

# Start the Whisper server
python setup_whisper.py server
```

### 2. Manual Setup
```bash
# Install required packages
pip install openai-whisper flask flask-cors

# Start the server
python whisper_server.py
```

## Available Models

| Model | Size | Speed | Quality | Recommended For |
|-------|------|-------|---------|-----------------|
| tiny | 39 MB | Fastest | Lowest | Quick testing |
| **base** | **74 MB** | **Fast** | **Good** | **General use** ⭐ |
| small | 244 MB | Medium | Better | Balanced quality |
| medium | 769 MB | Slower | Good | High quality needs |
| large | 1550 MB | Slowest | Best | Maximum quality |
| large-v2 | 1550 MB | Slowest | Best | Improved large |
| large-v3 | 1550 MB | Slowest | Best | Latest version |

**Recommendation**: Start with the `base` model for the best balance of speed and quality.

## Usage

### 1. Start the Server
```bash
# Option 1: Using setup script
python setup_whisper.py server

# Option 2: Direct server start
python whisper_server.py
```

The server will start on `http://localhost:8001`

### 2. Use in Web Application
1. Open your web media player
2. Load audio files using "Open Audiobook Folder"
3. Click "AI Whisper Audio Check" button
4. Select files and model
5. Click "Start Check" to transcribe

## Troubleshooting

### Server Won't Start
```bash
# Check Python version (3.8+ required)
python --version

# Reinstall dependencies
python setup_whisper.py install

# Test installation
python setup_whisper.py test
```

### Model Download Issues
- Models download automatically on first use
- Ensure stable internet connection for initial download
- Models are cached locally after first download

### Performance Tips
- Use `tiny` or `base` models for faster processing
- Close other applications to free up memory for larger models
- Consider using `small` model for good balance of speed/quality

### Common Errors

**"Module not found: whisper"**
```bash
pip install openai-whisper
```

**"Server connection failed"**
- Ensure server is running: `python whisper_server.py`
- Check if port 8001 is available
- Verify no firewall blocking localhost:8001

**"Model loading failed"**
- Check available disk space (models can be large)
- Ensure stable internet for initial model download
- Try a smaller model first (e.g., `tiny`)

## Server Endpoints

The local server provides these endpoints:

- `GET /health` - Check server status
- `GET /models` - List available models
- `POST /transcribe` - Transcribe audio file
- `POST /clear-cache` - Clear model cache

## Advanced Usage

### Custom Server Configuration
Edit `whisper_server.py` to change:
- Server port (default: 8001)
- Host binding (default: localhost)
- Model cache behavior

### Batch Processing
The web interface supports:
- Single file transcription
- Batch processing of all loaded files
- Progress tracking for multiple files
- Error handling and recovery

### Model Management
```bash
# List available models
python setup_whisper.py models

# Download specific model
python setup_whisper.py download base

# Clear model cache (frees memory)
curl -X POST http://localhost:8001/clear-cache
```

## System Requirements

### Minimum Requirements
- Python 3.8+
- 4 GB RAM (for tiny/base models)
- 1 GB free disk space

### Recommended Requirements
- Python 3.9+
- 8 GB RAM (for small/medium models)
- 5 GB free disk space (for multiple models)
- SSD storage (faster model loading)

### For Large Models
- 16 GB RAM (for large models)
- 10 GB free disk space
- GPU support (optional, for faster processing)

## Security Notes

- Server runs on localhost only (not accessible from network)
- No data is sent to external services
- Audio files are processed locally and temporarily
- No API keys or internet connection required for transcription

## Performance Comparison

Approximate processing times for 1 minute of audio:

| Model | CPU Time | GPU Time | Quality Score |
|-------|----------|----------|---------------|
| tiny | 5-10s | 2-3s | 6/10 |
| base | 10-20s | 3-5s | 7/10 |
| small | 30-60s | 5-10s | 8/10 |
| medium | 60-120s | 10-20s | 8.5/10 |
| large | 120-300s | 15-30s | 9/10 |

*Times vary based on hardware and audio complexity*
