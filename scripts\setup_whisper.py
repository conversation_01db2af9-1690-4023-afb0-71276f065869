#!/usr/bin/env python3
"""
Whisper Setup and Installation Script
====================================

This script helps users set up local Whisper for the web media player application.

Features:
- Check Python version compatibility
- Install required packages
- Download and test Whisper models
- Start the Whisper server
- Provide troubleshooting information

Usage:
    python setup_whisper.py [command]

Commands:
    install     - Install required packages
    test        - Test Whisper installation
    server      - Start the Whisper server
    models      - List and download models
    help        - Show this help message
"""

import sys
import subprocess
import os
import platform
from pathlib import Path

def print_header(title):
    """Print a formatted header."""
    print("\n" + "=" * 60)
    print(f"🎧 {title}")
    print("=" * 60)

def print_step(step, description):
    """Print a formatted step."""
    print(f"\n📋 Step {step}: {description}")
    print("-" * 40)

def check_python_version():
    """Check if Python version is compatible."""
    print_step(1, "Checking Python Version")
    
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Error: Python 3.8 or higher is required")
        print("Please upgrade Python and try again")
        return False
    
    print("✅ Python version is compatible")
    return True

def install_packages():
    """Install required packages."""
    print_step(2, "Installing Required Packages")
    
    packages = [
        "openai-whisper",
        "flask", 
        "flask-cors"
    ]
    
    for package in packages:
        print(f"Installing {package}...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", package], 
                         check=True, capture_output=True, text=True)
            print(f"✅ {package} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}: {e}")
            return False
    
    print("\n✅ All packages installed successfully!")
    return True

def test_whisper():
    """Test Whisper installation."""
    print_step(3, "Testing Whisper Installation")
    
    try:
        import whisper
        print("✅ Whisper module imported successfully")
        
        # Test loading a small model
        print("Testing model loading (this may take a moment)...")
        model = whisper.load_model("tiny")
        print("✅ Whisper model loaded successfully")
        
        return True
    except ImportError:
        print("❌ Whisper is not installed properly")
        return False
    except Exception as e:
        print(f"❌ Error testing Whisper: {e}")
        return False

def list_models():
    """List available Whisper models."""
    print_step(4, "Available Whisper Models")
    
    models = [
        ("tiny", "39 MB", "Fastest, lowest quality"),
        ("base", "74 MB", "Fast, good quality - RECOMMENDED"),
        ("small", "244 MB", "Balanced speed/quality"),
        ("medium", "769 MB", "Good quality"),
        ("large", "1550 MB", "Best quality"),
        ("large-v2", "1550 MB", "Improved large model"),
        ("large-v3", "1550 MB", "Latest large model")
    ]
    
    print("Model sizes and descriptions:")
    for name, size, desc in models:
        marker = " ⭐" if name == "base" else ""
        print(f"  • {name:<10} ({size:<8}) - {desc}{marker}")
    
    print("\n💡 Tip: Start with 'base' model for best balance of speed and quality")

def download_model(model_name):
    """Download a specific model."""
    print(f"\nDownloading {model_name} model...")
    try:
        import whisper
        model = whisper.load_model(model_name)
        print(f"✅ {model_name} model downloaded and ready")
        return True
    except Exception as e:
        print(f"❌ Failed to download {model_name}: {e}")
        return False

def start_server():
    """Start the Whisper server."""
    print_step(5, "Starting Whisper Server")
    
    server_script = Path(__file__).parent / "whisper_server.py"
    
    if not server_script.exists():
        print("❌ whisper_server.py not found")
        print("Please ensure whisper_server.py is in the same directory")
        return False
    
    print("Starting server...")
    print("Note: Use Ctrl+C to stop the server")
    
    try:
        subprocess.run([sys.executable, str(server_script)])
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

def show_help():
    """Show help information."""
    print(__doc__)

def main():
    """Main function."""
    print_header("Local Whisper Setup")
    
    if len(sys.argv) < 2:
        command = "install"
    else:
        command = sys.argv[1].lower()
    
    if command == "help":
        show_help()
        return
    
    elif command == "install":
        print("🚀 Setting up local Whisper for audio transcription...")
        
        if not check_python_version():
            return
        
        if not install_packages():
            return
        
        if not test_whisper():
            return
        
        list_models()
        
        # Ask user if they want to download the base model
        print("\n" + "=" * 60)
        response = input("Download the recommended 'base' model now? (y/n): ").lower()
        if response in ['y', 'yes']:
            download_model("base")
        
        print("\n" + "=" * 60)
        print("🎉 Setup Complete!")
        print("\nNext steps:")
        print("1. Start the Whisper server: python setup_whisper.py server")
        print("2. Open your web application")
        print("3. Click 'AI Whisper Audio Check' to test transcription")
        print("=" * 60)
    
    elif command == "test":
        if check_python_version() and test_whisper():
            print("\n✅ Whisper is working correctly!")
        else:
            print("\n❌ Whisper setup has issues. Try running: python setup_whisper.py install")
    
    elif command == "server":
        start_server()
    
    elif command == "models":
        list_models()
        print("\nTo download a model, use: python setup_whisper.py download <model_name>")
    
    elif command == "download":
        if len(sys.argv) < 3:
            print("Usage: python setup_whisper.py download <model_name>")
            list_models()
        else:
            model_name = sys.argv[2]
            download_model(model_name)
    
    else:
        print(f"Unknown command: {command}")
        print("Use 'python setup_whisper.py help' for available commands")

if __name__ == "__main__":
    main()
