
export const arcticFrostTheme = {
  name: 'Arctic Frost',
  properties: {
    '--bg-primary': '#E0F2F7', // Very light cyan/blue
    '--bg-secondary': '#FFFFFF', // White
    '--bg-app-container': '#FFFFFF',
    '--bg-panel': '#F0FAFD', // Slightly off-white panel
    '--bg-header': '#4A90E2', // Clear Blue Header
    '--bg-status-bar': '#3170B0', // Deeper Blue Status Bar
    '--bg-modal-backdrop': 'rgba(0, 0, 0, 0.4)',
    '--bg-modal-content': '#FFFFFF',
    '--bg-input': '#FFFFFF',
    '--bg-input-disabled': '#D0E0E8',
    '--bg-list-box': '#FFFFFF',
    '--bg-list-item-hover': '#D6EDF5',
    '--bg-list-item-selected': '#4A90E2', // Selected item matches header
    '--bg-ssml-editor-view': '#E0F2F7',
    '--bg-ssml-instructions': '#D6EDF5',
    '--bg-ssml-preview-modal-content': '#E8F5F9',

    '--text-primary': '#2C3E50', // Dark Slate Blue
    '--text-secondary': '#5A6A7C', // Medium Slate
    '--text-panel-heading': '#3170B0', // Deeper Blue for headings
    '--text-header': '#FFFFFF',
    '--text-status-bar': '#FFFFFF',
    '--text-button-primary': '#FFFFFF',
    '--text-list-item-selected': '#FFFFFF',
    '--text-placeholder': '#7C8A99',
    '--text-highlighted': '#2C3E50',
    '--text-search-highlighted': '#000000',
    '--text-ssml-editor-file-info': '#5A6A7C',
    '--text-ssml-instructions': '#2C3E50',
    '--text-ssml-tag': '#3170B0', // Deeper Blue for tags
    '--text-ssml-attr-name': '#D9534F', // Muted Red
    '--text-ssml-attr-value': '#5CB85C', // Muted Green
    '--text-ssml-comment': '#7C8A99',

    '--border-primary': '#B0C7D3', // Light blue-gray borders
    '--border-secondary': '#C8DDE6',
    '--border-input': '#A0B8C3',
    '--border-ssml-text-widget': '#A0B8C3',
    '--border-ssml-instructions': '#B0C7D3',
    '--border-ssml-preview-modal-content': '#B0C7D3',

    '--accent-primary': '#4A90E2', // Clear Blue
    '--accent-secondary': '#5BC0DE', // Light Teal/Cyan
    '--accent-secondary-hover': '#4AB8D6', // Darker Light Teal/Cyan
    '--accent-playback': '#5CB85C', // Muted Green for playback
    '--accent-playback-hover': '#4CAF4F', // Darker Muted Green
    '--accent-highlight': '#AEE6FF', // Very Light Blue highlight
    '--accent-search-highlight': '#FFFF99', // Pale Yellow
    '--accent-volume-slider': '#4A90E2',

    '--button-primary-bg': '#5BC0DE', // Light Teal/Cyan buttons
    '--button-primary-hover-bg': '#4AB8D6',
    '--button-primary-active-bg': '#3170B0', // Active nav button matches status bar
    '--button-disabled-bg': '#C8DDE6',
    '--button-file-input-bg': '#4A90E2',
    '--button-file-input-hover-bg': '#3F80D0',
    
    '--button-accent-bg': '#5CB85C', // Muted Green
    '--button-accent-hover-bg': '#4CAF4F',
    '--button-warning-bg': '#F0AD4E', // Muted Orange
    '--button-warning-hover-bg': '#EEA03E',
    '--button-info-bg': '#4A90E2',
    '--button-info-hover-bg': '#3F80D0',
    
    '--shadow-light': 'rgba(0,0,0,0.03)',
    '--shadow-medium': 'rgba(0,0,0,0.07)',
    '--shadow-modal': 'rgba(0,0,0,0.15)',
    '--shadow-inset-active-nav': 'rgba(0,0,0,0.1)',

    '--icon-primary': '#5BC0DE', // Light Teal/Cyan icons
    '--icon-ms-tts': '#4A90E2', // Clear Blue for MS TTS icon
    '--icon-selected-item': '#FFFFFF',

    '--status-processing-color': '#4A90E2',
    '--status-ok-color': '#5CB85C',
    '--status-flagged-color': '#F0AD4E',
    '--status-error-color': '#D9534F',
    '--status-corrected-color': '#5BC0DE',
    '--status-no-text-color': '#7C8A99',
  }
};